[workspace]
members = [
    "autopush-common",
    "autoendpoint",
    "autoconnect",
    "autoconnect/autoconnect-common",
    "autoconnect/autoconnect-settings",
    "autoconnect/autoconnect-web",
    "autoconnect/autoconnect-ws",
    "autoconnect/autoconnect-ws/autoconnect-ws-sm",
]
resolver = "2"

[workspace.package]
version = "1.76.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <mdr<PERSON><EMAIL>>",
]
edition = "2021"
rust-version = "1.86.0"

[workspace.dependencies]
# ideally, this would contain any crates that are shared between crates.
# there are some lingering code interdependencies that prevent that, but it should
# remain a goal for the overall refactor.

deadpool = { version = "0.12", features = ["managed", "rt_tokio_1"] }
actix = "0.13"
actix-cors = "0.7"
actix-http = "3.11"
actix-rt = "2.7"
actix-test = "0.1"
actix-web = "4.11"
actix-ws = "0.3"
backtrace = "0.3"
base64 = "0.22"
bytes = "1.4"
bytestring = "1.2"
cadence = "1.2"
chrono = "0.4"
config = "0.15"
ctor = "0.4"
docopt = "1.1"
env_logger = "0.11"
fernet = "0.2.0"
futures = { version = "0.3", features = ["compat"] }
futures-util = { version = "0.3", features = [
    "async-await",
    "compat",
    "sink",
    "io",
] }
futures-locks = "0.7"
hex = "0.4.2"
httparse = "1.3"
hyper = "1.6"
lazy_static = "1.4"
log = { version = "0.4", features = [
    "max_level_debug",
    "release_max_level_info",
] }
mockall = "0.13"
mozsvc-common = "0.2"
openssl = { version = "0.10" }
rand = "0.9"
regex = "1.4"
reqwest = { version = "0.12", features = ["json", "blocking"] }
sentry = { version = "0.38", features = [
    "debug-logs",
] } # Using debug-logs avoids https://github.com/getsentry/sentry-rust/issues/237
sentry-core = { version = "0.38" }
sentry-actix = "0.38"
sentry-backtrace = "0.38"
serde = "1.0"
serde_derive = "1.0"
serde_json = "1.0"
slog = { version = "2.7", features = [
    "dynamic-keys",
    "max_level_trace",
    "release_max_level_info",
] }
slog-async = "2.6"
slog-envlogger = "2.2.0"
slog-mozlog-json = "0.1"
slog-scope = "4.4"
slog-stdlog = "4.1"
slog-term = "2.6"
strum = { version = "0.27", features = ["derive"] }
strum_macros = "0.27"
thiserror = "2.0"
tokio = "1.45"
tokio-compat-02 = "0.2"
tokio-core = "0.1"
tokio-io = "0.1"
tokio-openssl = "0.6"
uuid = { version = "1.17", features = ["serde", "v4"] }
url = "2.5"

autoconnect = { path = "./autoconnect" }
autoconnect_common = { path = "./autoconnect/autoconnect-common" }
autoconnect_settings = { path = "./autoconnect/autoconnect-settings" }
autoconnect_web = { path = "./autoconnect/autoconnect-web" }
autoconnect_ws = { path = "./autoconnect/autoconnect-ws" }
autoconnect_ws_clientsm = { path = "./autoconnect/autoconnect-ws/autoconnect-ws-clientsm" }
autopush_common = { path = "./autopush-common", features = ["bigtable"] }

[workspace.lints.clippy]
result_large_err = "allow" # 1.87.0 will flag ApiError as too large.

[profile.release]
debug = 1
