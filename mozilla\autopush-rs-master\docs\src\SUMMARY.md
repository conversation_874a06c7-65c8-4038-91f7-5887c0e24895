# Summary
<!-- NOTE: `mdbook build` will create documents if they're not present. It uses
     the path specified in the parenthesis. It has no idea about internal links
     so (foo.md#bar) will create a doc named "foo.md#bar".
-->
* [Introduction](index.md)
* [General Architecture](architecture.md)
  * [Push Reliability Tracking](reliability.md)
  * [Legacy table rotation](table_rotation.md)
* [Install](install.md)
  * [Apple Push Notification (APNs) guide](apns.md)
  * [Google Firebase Cloud Messaging (FCM) guide](fcm.md)
* [Running](running.md)

## Developing

* [Style](style.md)
* [Testing](testing.md)
* [Release Process](releasing.md)

## Reference

* [HTTP Endpoints for Notifications](http.md)
* [Error codes](errors.md)
* [Glossary](glossary.md)
* [Why rust?](rust.md)
