'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  FolderOpen,
  FileText,
  Users,
  Settings,
  Upload,
  BarChart3,
  Calendar,
  MessageSquare,
  HelpCircle,
  Building2,
} from 'lucide-react';

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  roles?: string[];
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Projects',
    href: '/projects',
    icon: FolderOpen,
    badge: 'New',
  },
  {
    title: 'Documents',
    href: '/documents',
    icon: FileText,
  },
  {
    title: 'Upload',
    href: '/upload',
    icon: Upload,
  },
  {
    title: 'Calendar',
    href: '/calendar',
    icon: Calendar,
  },
  {
    title: 'Messages',
    href: '/messages',
    icon: MessageSquare,
    badge: 3,
  },
];

const adminItems: NavItem[] = [
  {
    title: 'Users',
    href: '/admin/users',
    icon: Users,
    roles: ['admin'],
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    roles: ['admin', 'project_manager'],
  },
];

const bottomItems: NavItem[] = [
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
  {
    title: 'Help & Support',
    href: '/help',
    icon: HelpCircle,
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ isOpen = true, onClose }) => {
  const pathname = usePathname();
  const { user } = useAuth();

  const hasRole = (roles?: string[]) => {
    if (!roles || !user) return true;
    return roles.includes(user.role);
  };

  const NavLink: React.FC<{ item: NavItem }> = ({ item }) => {
    const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
    const Icon = item.icon;

    if (!hasRole(item.roles)) return null;

    return (
      <Button
        variant={isActive ? 'secondary' : 'ghost'}
        className={cn(
          'w-full justify-start gap-3 h-11 px-3',
          isActive && 'bg-secondary font-medium'
        )}
        asChild
      >
        <Link href={item.href} onClick={onClose}>
          <Icon className="h-5 w-5" />
          <span className="flex-1 text-left">{item.title}</span>
          {item.badge && (
            <Badge 
              variant={typeof item.badge === 'number' ? 'destructive' : 'default'}
              className="ml-auto h-5 px-2 text-xs"
            >
              {item.badge}
            </Badge>
          )}
        </Link>
      </Button>
    );
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          'fixed left-0 top-16 z-50 h-[calc(100vh-4rem)] w-64 transform border-r bg-background transition-transform duration-200 ease-in-out md:relative md:top-0 md:h-[calc(100vh-4rem)] md:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex h-full flex-col">
          {/* Logo section for mobile */}
          <div className="flex items-center gap-2 p-4 md:hidden">
            <div className="text-foreground font-bold">
              <span className="text-lg">B</span>
              <span className="text-xs">UILDERS</span>
              <br />
              <span className="text-xs">BY DESIGN</span>
              <span className="text-xs ml-1">GROUP</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 p-4">
            {/* Main navigation */}
            <div className="space-y-1">
              {navigationItems.map((item) => (
                <NavLink key={item.href} item={item} />
              ))}
            </div>

            {/* Admin section */}
            {adminItems.some(item => hasRole(item.roles)) && (
              <>
                <Separator className="my-4" />
                <div className="space-y-1">
                  <h3 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                    Administration
                  </h3>
                  {adminItems.map((item) => (
                    <NavLink key={item.href} item={item} />
                  ))}
                </div>
              </>
            )}

            {/* Bottom section */}
            <Separator className="my-4" />
            <div className="space-y-1">
              {bottomItems.map((item) => (
                <NavLink key={item.href} item={item} />
              ))}
            </div>
          </nav>

          {/* User info footer */}
          <div className="border-t p-4">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Building2 className="h-4 w-4 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {user?.company || 'Builders By Design'}
                </p>
                <p className="text-xs text-muted-foreground">
                  Version 1.0.0
                </p>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};
