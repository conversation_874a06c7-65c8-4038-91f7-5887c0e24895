// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  company?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'project_manager' | 'client';

// Authentication Types
export interface AuthResponse {
  user: User;
  token: string;
  exp: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  startDate: string;
  endDate?: string;
  budget?: number;
  client: User;
  projectManager: User;
  address?: string;
  documents: Document[];
  createdAt: string;
  updatedAt: string;
}

export type ProjectStatus = 'planning' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled';

// Document Types
export interface Document {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  filesize: number;
  url: string;
  alt?: string;
  project: string | Project;
  uploadedBy: User;
  category: DocumentCategory;
  version: number;
  isLatest: boolean;
  createdAt: string;
  updatedAt: string;
}

export type DocumentCategory = 'drawing' | 'specification' | 'contract' | 'photo' | 'report' | 'other';

// API Response Types
export interface ApiResponse<T = any> {
  docs?: T[];
  totalDocs?: number;
  limit?: number;
  totalPages?: number;
  page?: number;
  pagingCounter?: number;
  hasPrevPage?: boolean;
  hasNextPage?: boolean;
  prevPage?: number | null;
  nextPage?: number | null;
}

export interface ApiError {
  message: string;
  errors?: Array<{
    message: string;
    path: string;
  }>;
}

// Form Types
export interface ProjectFormData {
  name: string;
  description: string;
  status: ProjectStatus;
  startDate: string;
  endDate?: string;
  budget?: number;
  address?: string;
  clientId: string;
  projectManagerId: string;
}

export interface DocumentUploadData {
  file: File;
  category: DocumentCategory;
  projectId: string;
  alt?: string;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Dashboard Types
export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalDocuments: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'project_created' | 'document_uploaded' | 'project_updated' | 'user_added';
  message: string;
  user: User;
  project?: Project;
  document?: Document;
  createdAt: string;
}

// Navigation Types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  badge?: string | number;
  children?: NavItem[];
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system';

// File Upload Types
export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  status?: ProjectStatus[];
  category?: DocumentCategory[];
  dateRange?: {
    from: string;
    to: string;
  };
  userId?: string;
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: string;
  actionUrl?: string;
}
