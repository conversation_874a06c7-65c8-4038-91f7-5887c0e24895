[package]
name = "autoconnect_settings"
version.workspace = true
authors.workspace = true
edition.workspace = true

[dependencies]
cadence.workspace = true
config.workspace = true
fernet.workspace = true
lazy_static.workspace = true
mozsvc-common.workspace = true
reqwest.workspace = true
serde.workspace = true
serde_derive.workspace = true
serde_json.workspace = true
slog.workspace = true
slog-scope.workspace = true
tokio.workspace = true

autoconnect_common.workspace = true
autopush_common.workspace = true

[features]
# specify the default via the calling crate, in order to simplify default chains.
bigtable = ["autopush_common/bigtable"]
emulator = ["bigtable"]
reliable_report = ["autopush_common/reliable_report"]

[lints]
workspace = true
