import { apiPost, apiGet, API_ENDPOINTS } from './config';
import { AuthResponse, LoginCredentials, User } from '../types';

/**
 * Authentication API service
 */
export const authApi = {
  /**
   * Login user with email and password
   */
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiPost<AuthResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      );

      if (!response.token || !response.user) {
        throw new Error('Invalid response from login API');
      }

      return response;
    } catch (error: any) {
      // Transform common error messages to be more user-friendly
      if (error.message?.includes('Invalid login credentials')) {
        throw new Error('Invalid email or password. Please try again.');
      }
      
      if (error.message?.includes('Account locked')) {
        throw new Error('Your account has been temporarily locked. Please contact support.');
      }

      if (error.message?.includes('Email not verified')) {
        throw new Error('Please verify your email address before logging in.');
      }

      throw error;
    }
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    try {
      await apiPost(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      // Don't throw on logout errors - we want to clear local state regardless
      console.warn('Logout API error:', error);
    }
  },

  /**
   * Get current user data
   */
  getCurrentUser: async (): Promise<User> => {
    try {
      const response = await apiGet<{ user: User }>(API_ENDPOINTS.AUTH.ME);
      
      if (!response.user) {
        throw new Error('Invalid user data received');
      }

      return response.user;
    } catch (error: any) {
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        throw new Error('Session expired. Please log in again.');
      }
      
      throw error;
    }
  },

  /**
   * Refresh authentication token
   */
  refreshToken: async (): Promise<AuthResponse> => {
    try {
      const response = await apiPost<AuthResponse>(API_ENDPOINTS.AUTH.REFRESH);
      
      if (!response.token || !response.user) {
        throw new Error('Invalid response from refresh token API');
      }

      return response;
    } catch (error: any) {
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        throw new Error('Session expired. Please log in again.');
      }
      
      throw error;
    }
  },

  /**
   * Validate current session
   */
  validateSession: async (): Promise<boolean> => {
    try {
      await apiGet(API_ENDPOINTS.AUTH.ME);
      return true;
    } catch (error) {
      return false;
    }
  },
};

/**
 * Helper function to check if user has required role
 */
export const checkUserRole = (user: User | null, requiredRoles: string[]): boolean => {
  if (!user) return false;
  return requiredRoles.includes(user.role);
};

/**
 * Helper function to get user display name
 */
export const getUserDisplayName = (user: User | null): string => {
  if (!user) return 'Unknown User';
  return `${user.firstName} ${user.lastName}`.trim() || user.email;
};

/**
 * Helper function to get user initials for avatar
 */
export const getUserInitials = (user: User | null): string => {
  if (!user) return 'U';
  
  const firstName = user.firstName?.charAt(0)?.toUpperCase() || '';
  const lastName = user.lastName?.charAt(0)?.toUpperCase() || '';
  
  if (firstName && lastName) {
    return firstName + lastName;
  }
  
  if (firstName) {
    return firstName;
  }
  
  return user.email?.charAt(0)?.toUpperCase() || 'U';
};

/**
 * Helper function to format user role for display
 */
export const formatUserRole = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'project_manager':
      return 'Project Manager';
    case 'client':
      return 'Client';
    default:
      return role.charAt(0).toUpperCase() + role.slice(1);
  }
};
