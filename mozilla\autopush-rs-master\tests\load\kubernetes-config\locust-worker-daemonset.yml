---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: "locust-worker-sysctl"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: "locust-worker"
  template:
    metadata:
      labels:
        app.kubernetes.io/name: "locust-worker"
    spec:
      nodeSelector:
        cloud.google.com/gke-nodepool: "locust-workers"
      hostNetwork: true
      initContainers:
        - name: sysctl
          image: alpine:3
          command:
            - /bin/sh
            - -c
            - |
              sysctl net.netfilter.nf_conntrack_max=1048576
          securityContext:
            privileged: true
          resources:
            requests:
              cpu: 10m
              memory: 10Mi
            limits:
              cpu: 10m
              memory: 10Mi
      containers:
        - name: sleep
          image: alpine:3
          command:
            - /bin/sh
            - -c
            - |
              while true; do sleep 60s; done
          resources:
            requests:
              cpu: 10m
              memory: 10Mi
            limits:
              cpu: 10m
              memory: 10Mi
