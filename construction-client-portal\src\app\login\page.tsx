'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/loading';
import { Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(1, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, authLoading, router]);

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      await login(data);
      
      // Success toast is handled in AuthContext
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading spinner while checking auth status
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header Navigation */}
      <header className="w-full px-6 py-6">
        <nav className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center">
            <div className="text-white font-bold">
              <div className="text-2xl leading-none">
                <span className="text-3xl">B</span>
                <span className="text-sm">UILDERS</span>
              </div>
              <div className="text-xs leading-none mt-1">
                <span>BY DESIGN</span>
                <span className="ml-1 text-gray-400">GROUP</span>
              </div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-8 text-gray-300 text-sm">
            <a href="#" className="hover:text-white transition-colors">About</a>
            <a href="#" className="hover:text-white transition-colors">Projects</a>
            <a href="#" className="hover:text-white transition-colors">Services</a>
            <a href="#" className="hover:text-white transition-colors">Testimonials</a>
            <a href="#" className="hover:text-white transition-colors">Partners</a>
            <a href="#" className="hover:text-white transition-colors">Contact</a>
            <span className="text-white font-medium">Client Portal</span>
          </div>
        </nav>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-lg">

          {/* Login Form */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-light text-gray-300 tracking-wide">Client Portal Login</h1>
          </div>

          <div className="space-y-8 max-w-md mx-auto">
            {error && (
              <div className="mb-6 p-3 bg-red-900/20 border border-red-800 rounded text-red-300 text-sm">
                {error}
              </div>
            )}

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-400 text-sm font-light">Username</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder=""
                          disabled={isLoading}
                          className="h-14 bg-transparent border-0 border-b border-gray-700 rounded-none text-white placeholder-gray-500 focus:border-gray-500 focus:ring-0 text-lg px-0 pb-2"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm mt-2" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-400 text-sm font-light">Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? 'text' : 'password'}
                            placeholder=""
                            disabled={isLoading}
                            className="h-14 bg-transparent border-0 border-b border-gray-700 rounded-none text-white placeholder-gray-500 focus:border-gray-500 focus:ring-0 text-lg px-0 pb-2 pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-6 h-8 px-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-500" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-500" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm mt-2" />
                    </FormItem>
                  )}
                />

                <div className="text-right pt-4">
                  <a href="#" className="text-gray-400 text-sm underline hover:text-gray-300 font-light">
                    forgotten password?
                  </a>
                </div>

                <div className="pt-8">
                  <Button
                    type="submit"
                    className="w-full h-14 bg-transparent border border-gray-600 text-white hover:bg-gray-900 hover:border-gray-500 transition-colors font-light tracking-[0.2em] text-sm"
                    disabled={isLoading}
                    variant="outline"
                  >
                    {isLoading ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        SIGNING IN...
                      </>
                    ) : (
                      'LOGIN'
                    )}
                  </Button>
                </div>
              </form>
            </Form>

            {/* Demo Credentials */}
            <div className="mt-8 p-4 bg-gray-900/50 rounded border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Demo Credentials</h4>
              <div className="space-y-1 text-xs text-gray-400">
                <p><strong className="text-gray-300">Admin:</strong> <EMAIL> / admin123</p>
                <p><strong className="text-gray-300">Project Manager:</strong> <EMAIL> / pm123</p>
                <p><strong className="text-gray-300">Client:</strong> <EMAIL> / client123</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
