import { NextRequest, NextResponse } from 'next/server';

// Mock user database
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: '<PERSON>',
    lastName: 'Admin',
    role: 'admin',
    avatar: null,
    phone: '+****************',
    company: 'Builders By Design',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'pm123',
    firstName: 'Sarah',
    lastName: 'Manager',
    role: 'project_manager',
    avatar: null,
    phone: '+****************',
    company: 'Builders By Design',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'client123',
    firstName: '<PERSON>',
    lastName: 'Client',
    role: 'client',
    avatar: null,
    phone: '+****************',
    company: 'ABC Industries',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

// Simple JWT token generation (for demo purposes only)
function generateMockToken(user: any): string {
  const payload = {
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      avatar: user.avatar,
      phone: user.phone,
      company: user.company,
    },
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    iat: Math.floor(Date.now() / 1000),
  };
  
  // In a real app, you'd use a proper JWT library with signing
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user
    const user = mockUsers.find(u => u.email === email && u.password === password);
    
    if (!user) {
      return NextResponse.json(
        { message: 'Invalid login credentials' },
        { status: 401 }
      );
    }

    // Generate token
    const token = generateMockToken(user);
    const exp = Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60);

    // Remove password from user object
    const { password: _, ...userWithoutPassword } = user;

    // Return success response
    return NextResponse.json({
      user: userWithoutPassword,
      token,
      exp,
      message: 'Login successful'
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
