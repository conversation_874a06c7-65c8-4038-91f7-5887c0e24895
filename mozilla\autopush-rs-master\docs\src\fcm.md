# Configuring for Google GCM/FCM

<!-- TODO: finish this doc -->

Google's [Firebase Cloud
Messaging](https://firebase.google.com/docs/cloud-messaging/) (FCM)
superceded Google Cloud Messaging (GCM). The server setup [process is well documented](https://firebase.google.com/docs/cloud-messaging/server), with autopush using the [FTM HTTP v1 API](https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages) protocol.

## Authorization

FCM requires a server authentication key. These keys are specified in the `autoendpoint` configuration as the environment variable `AUTOEND__FCM__CREDENTIALS` (or configuration file option `[fcm] server_credentials`) as a serialized JSON structure containing the bridge project ID and either the contents of the credential file generated by the [Service Account generated key](https://firebase.google.com/docs/cloud-messaging/auth-server#provide-credentials-manually), or a path to the file containing the credentials

As an example, let's assume we create a Push recipient application with a Google Cloud Project ID of `random-example-123`. Since our clients could be using various alternative bridges (for testing, stage, etc.) we would use an alternate identifier for the `instance_id`

If we saved the sample credentials we received from Google to `./keys/credentials.json`, it might look like:

```json
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## Autoendpoint Configuration

If we wished to "in-line" the credentials for an instance_id of `default`, our environment variable would look like:

```bash
AUTOEND__FCM__CREDENTIALS='{"default":{"project_id":"random-example-123","credential":"{\"type\": \"service_account\",\"project_id\":\"random-example-123\",\"private_key_id\": \"abc..890\",\"private_key\": \"---***---\",\"client_email\": \"***\",\"client_id\": \"***\",\"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\"token_uri\": \"https://oauth2.googleapis.com/token\",\"auth_provider_x509_cert_url\": \"https://www.googleapis.com/***\",\"client_x509_cert_url\":\"***\"}"}'
```

We could also just point to the relative path of the file using:

```bash
AUTOEND__FCM__CREDENTIALS='{"default":{"project_id":"random-example-123","credential":"keys/credentials.json"}'
```

Only `autoendpoint` uses the bridge interface, so you do not need to specify this configuration for `autoconnect`.
