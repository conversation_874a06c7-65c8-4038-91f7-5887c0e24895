import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  FileX, 
  Wifi, 
  Shield,
  Bug
} from 'lucide-react';

interface ErrorMessageProps {
  title?: string;
  message: string;
  variant?: 'default' | 'destructive';
  className?: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Error',
  message,
  variant = 'destructive',
  className
}) => {
  return (
    <Alert variant={variant} className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};

interface ErrorPageProps {
  title?: string;
  message?: string;
  statusCode?: number;
  onRetry?: () => void;
  onGoHome?: () => void;
  className?: string;
}

export const ErrorPage: React.FC<ErrorPageProps> = ({
  title,
  message,
  statusCode,
  onRetry,
  onGoHome,
  className
}) => {
  const getErrorDetails = () => {
    switch (statusCode) {
      case 404:
        return {
          icon: FileX,
          title: title || 'Page Not Found',
          message: message || 'The page you are looking for does not exist.',
        };
      case 403:
        return {
          icon: Shield,
          title: title || 'Access Denied',
          message: message || 'You do not have permission to access this resource.',
        };
      case 500:
        return {
          icon: Bug,
          title: title || 'Server Error',
          message: message || 'Something went wrong on our end. Please try again later.',
        };
      case 0:
        return {
          icon: Wifi,
          title: title || 'Connection Error',
          message: message || 'Unable to connect to the server. Please check your internet connection.',
        };
      default:
        return {
          icon: AlertTriangle,
          title: title || 'Something went wrong',
          message: message || 'An unexpected error occurred. Please try again.',
        };
    }
  };

  const { icon: Icon, title: errorTitle, message: errorMessage } = getErrorDetails();

  return (
    <div className={cn(
      'flex items-center justify-center min-h-[400px] p-4',
      className
    )}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
            <Icon className="h-8 w-8 text-destructive" />
          </div>
          <CardTitle className="text-xl">{errorTitle}</CardTitle>
          <CardDescription className="text-center">
            {errorMessage}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-3">
          {onRetry && (
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
          {onGoHome && (
            <Button 
              variant="outline" 
              onClick={onGoHome}
              className="w-full"
            >
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent 
            error={this.state.error!} 
            resetError={this.resetError} 
          />
        );
      }

      return (
        <ErrorPage
          title="Application Error"
          message="Something went wrong in the application. Please refresh the page or try again later."
          onRetry={this.resetError}
          onGoHome={() => window.location.href = '/dashboard'}
        />
      );
    }

    return this.props.children;
  }
}

// Hook for error handling
export const useErrorHandler = () => {
  const handleError = (error: any, context?: string) => {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error);
    
    // You can add error reporting service here
    // e.g., Sentry, LogRocket, etc.
    
    return {
      title: 'Error',
      message: error?.message || 'An unexpected error occurred',
      statusCode: error?.status || error?.statusCode,
    };
  };

  return { handleError };
};

// Network error component
export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => {
  return (
    <ErrorPage
      statusCode={0}
      title="Connection Problem"
      message="Unable to connect to the server. Please check your internet connection and try again."
      onRetry={onRetry}
    />
  );
};

// Not found component
export const NotFound: React.FC<{ 
  resource?: string;
  onGoBack?: () => void;
}> = ({ 
  resource = 'page',
  onGoBack
}) => {
  return (
    <ErrorPage
      statusCode={404}
      title={`${resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found`}
      message={`The ${resource} you are looking for does not exist or has been moved.`}
      onGoHome={onGoBack || (() => window.history.back())}
    />
  );
};

// Access denied component
export const AccessDenied: React.FC<{ onGoHome?: () => void }> = ({ onGoHome }) => {
  return (
    <ErrorPage
      statusCode={403}
      title="Access Denied"
      message="You don't have permission to access this resource. Please contact your administrator if you believe this is an error."
      onGoHome={onGoHome || (() => window.location.href = '/dashboard')}
    />
  );
};
