#!/usr/bin/env python3
"""
Mozilla Autopush-rs Security Vulnerability Analyzer
Comprehensive security analysis tool for Mozilla's push notification service
Focus: Critical vulnerabilities for bug bounty submission
"""

import os
import re
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class SecurityFinding:
    """Represents a potential security vulnerability"""
    severity: str  # CRITICAL, HIGH, MEDIUM, LOW
    category: str  # e.g., "Authentication Bypass", "Injection", "Cryptographic"
    title: str
    description: str
    file_path: str
    line_number: int
    code_snippet: str
    impact: str
    recommendation: str
    cwe_id: Optional[str] = None
    cvss_score: Optional[float] = None

class MozillaAutopushSecurityAnalyzer:
    """Advanced security analyzer for Mozilla autopush-rs codebase"""
    
    def __init__(self, base_path: str = "mozilla/autopush-rs-master"):
        self.base_path = Path(base_path)
        self.findings: List[SecurityFinding] = []
        self.analyzed_files: Set[str] = set()
        
        # Critical security patterns to detect
        self.security_patterns = {
            # Authentication & Authorization
            'auth_bypass': [
                r'\.unwrap\(\).*auth',
                r'expect\(.*auth.*\)',
                r'auth.*=.*None',
                r'skip.*auth',
                r'bypass.*auth',
            ],
            
            # Input validation issues
            'input_validation': [
                r'\.parse\(\)\.unwrap\(\)',
                r'from_str\(\)\.unwrap\(\)',
                r'\.expect\(".*parse.*"\)',
                r'unsafe\s+{',
                r'transmute\s*\(',
            ],
            
            # Cryptographic issues
            'crypto_issues': [
                r'rand::random\(\)',
                r'SystemTime::now\(\)',
                r'thread_rng\(\)',
                r'weak.*key',
                r'hardcoded.*key',
                r'static.*key',
            ],
            
            # Injection vulnerabilities
            'injection': [
                r'format!\(.*\{.*\}.*\)',
                r'println!\(.*\{.*\}.*\)',
                r'query.*format',
                r'sql.*format',
                r'exec.*format',
            ],
            
            # Deserialization issues
            'deserialization': [
                r'serde_json::from_str\(',
                r'bincode::deserialize\(',
                r'from_slice\(',
                r'deserialize_with\(',
            ],
            
            # Race conditions
            'race_conditions': [
                r'Arc<Mutex<',
                r'RwLock<',
                r'unsafe.*static',
                r'lazy_static!',
                r'once_cell',
            ],
            
            # Information disclosure
            'info_disclosure': [
                r'debug!\(.*password',
                r'debug!\(.*token',
                r'debug!\(.*key',
                r'println!\(.*secret',
                r'eprintln!\(.*auth',
            ],
        }
        
        # High-risk file patterns
        self.critical_files = [
            'auth.rs', 'authorization*.rs', 'token*.rs', 'crypto*.rs',
            'handler.rs', 'session.rs', 'notification.rs', 'webpush.rs'
        ]

    def analyze_codebase(self) -> Dict:
        """Perform comprehensive security analysis"""
        print(f"🔍 Starting Mozilla Autopush-rs Security Analysis...")
        print(f"📁 Base path: {self.base_path}")
        
        # Analyze all Rust source files
        rust_files = list(self.base_path.rglob("*.rs"))
        print(f"📄 Found {len(rust_files)} Rust files to analyze")
        
        for file_path in rust_files:
            self._analyze_file(file_path)
        
        # Generate comprehensive report
        report = self._generate_report()
        
        # Save findings to JSON
        self._save_findings()
        
        return report

    def _analyze_file(self, file_path: Path):
        """Analyze individual Rust file for security issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            self.analyzed_files.add(str(file_path))
            
            # Check for security patterns
            for category, patterns in self.security_patterns.items():
                for pattern in patterns:
                    self._check_pattern(file_path, content, lines, pattern, category)
            
            # Perform specialized analysis for critical files
            if any(critical in file_path.name for critical in self.critical_files):
                self._analyze_critical_file(file_path, content, lines)
                
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")

    def _check_pattern(self, file_path: Path, content: str, lines: List[str], 
                      pattern: str, category: str):
        """Check for specific security pattern in file"""
        try:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                line_content = lines[line_num - 1] if line_num <= len(lines) else ""
                
                # Create security finding
                finding = self._create_finding(
                    file_path, line_num, line_content, pattern, category
                )
                
                if finding:
                    self.findings.append(finding)
                    
        except re.error as e:
            print(f"⚠️  Regex error in pattern {pattern}: {e}")

    def _analyze_critical_file(self, file_path: Path, content: str, lines: List[str]):
        """Specialized analysis for critical security files"""
        
        # Authentication bypass analysis
        if 'auth' in file_path.name.lower():
            self._analyze_auth_file(file_path, content, lines)
        
        # WebSocket handler analysis
        if 'handler.rs' in file_path.name or 'session.rs' in file_path.name:
            self._analyze_websocket_file(file_path, content, lines)
        
        # Notification handling analysis
        if 'notification.rs' in file_path.name:
            self._analyze_notification_file(file_path, content, lines)

    def _analyze_auth_file(self, file_path: Path, content: str, lines: List[str]):
        """Analyze authentication-related files for vulnerabilities"""
        
        # Check for timing attack vulnerabilities
        if 'memcmp::eq' not in content and ('==' in content or '!=' in content):
            for i, line in enumerate(lines):
                if ('token' in line.lower() or 'auth' in line.lower()) and ('==' in line or '!=' in line):
                    finding = SecurityFinding(
                        severity="HIGH",
                        category="Timing Attack",
                        title="Potential Timing Attack in Authentication",
                        description="String comparison may be vulnerable to timing attacks",
                        file_path=str(file_path),
                        line_number=i + 1,
                        code_snippet=line.strip(),
                        impact="Attackers could potentially extract authentication tokens through timing analysis",
                        recommendation="Use constant-time comparison functions like openssl::memcmp::eq",
                        cwe_id="CWE-208"
                    )
                    self.findings.append(finding)

    def _analyze_websocket_file(self, file_path: Path, content: str, lines: List[str]):
        """Analyze WebSocket handler files for vulnerabilities"""
        
        # Check for message injection vulnerabilities
        for i, line in enumerate(lines):
            if 'serde_json::from_str' in line and 'validate' not in content.lower():
                finding = SecurityFinding(
                    severity="HIGH",
                    category="Message Injection",
                    title="Unvalidated WebSocket Message Deserialization",
                    description="WebSocket messages are deserialized without proper validation",
                    file_path=str(file_path),
                    line_number=i + 1,
                    code_snippet=line.strip(),
                    impact="Attackers could send malicious WebSocket messages to trigger unexpected behavior",
                    recommendation="Implement strict message validation before deserialization",
                    cwe_id="CWE-502"
                )
                self.findings.append(finding)

    def _analyze_notification_file(self, file_path: Path, content: str, lines: List[str]):
        """Analyze notification handling for security issues"""
        
        # Check for notification spoofing
        for i, line in enumerate(lines):
            if 'channel_id' in line and 'validate' not in line.lower():
                if '=' in line and 'extract' in line:
                    finding = SecurityFinding(
                        severity="MEDIUM",
                        category="Authorization",
                        title="Potential Channel ID Validation Issue",
                        description="Channel ID extraction may lack proper validation",
                        file_path=str(file_path),
                        line_number=i + 1,
                        code_snippet=line.strip(),
                        impact="Attackers might be able to send notifications to unauthorized channels",
                        recommendation="Implement strict channel ID validation and authorization checks",
                        cwe_id="CWE-863"
                    )
                    self.findings.append(finding)

    def _create_finding(self, file_path: Path, line_num: int, line_content: str, 
                       pattern: str, category: str) -> Optional[SecurityFinding]:
        """Create a security finding based on pattern match"""
        
        # Define severity and details based on category
        severity_map = {
            'auth_bypass': 'CRITICAL',
            'injection': 'HIGH',
            'crypto_issues': 'HIGH',
            'deserialization': 'HIGH',
            'input_validation': 'MEDIUM',
            'race_conditions': 'MEDIUM',
            'info_disclosure': 'LOW'
        }
        
        severity = severity_map.get(category, 'MEDIUM')
        
        # Skip false positives
        if self._is_false_positive(line_content, pattern, category):
            return None
        
        return SecurityFinding(
            severity=severity,
            category=category.replace('_', ' ').title(),
            title=f"Potential {category.replace('_', ' ').title()} Issue",
            description=f"Pattern '{pattern}' detected in security-sensitive context",
            file_path=str(file_path),
            line_number=line_num,
            code_snippet=line_content.strip(),
            impact=self._get_impact_description(category),
            recommendation=self._get_recommendation(category),
            cwe_id=self._get_cwe_id(category)
        )

    def _is_false_positive(self, line: str, pattern: str, category: str) -> bool:
        """Check if finding is likely a false positive"""
        
        # Skip test files
        if 'test' in line.lower() or '#[test]' in line:
            return True
        
        # Skip comments
        if line.strip().startswith('//'):
            return True
        
        # Skip documentation
        if line.strip().startswith('///'):
            return True
        
        return False

    def _get_impact_description(self, category: str) -> str:
        """Get impact description for vulnerability category"""
        impacts = {
            'auth_bypass': "Complete authentication bypass allowing unauthorized access",
            'injection': "Code execution or data manipulation through injection attacks",
            'crypto_issues': "Cryptographic weaknesses leading to data exposure",
            'deserialization': "Remote code execution through malicious payloads",
            'input_validation': "Data corruption or unexpected application behavior",
            'race_conditions': "Data corruption or privilege escalation",
            'info_disclosure': "Sensitive information exposure to unauthorized parties"
        }
        return impacts.get(category, "Potential security impact")

    def _get_recommendation(self, category: str) -> str:
        """Get remediation recommendation for vulnerability category"""
        recommendations = {
            'auth_bypass': "Implement proper authentication checks and error handling",
            'injection': "Use parameterized queries and input sanitization",
            'crypto_issues': "Use cryptographically secure random number generators",
            'deserialization': "Validate and sanitize all deserialized data",
            'input_validation': "Implement comprehensive input validation",
            'race_conditions': "Use proper synchronization mechanisms",
            'info_disclosure': "Remove sensitive information from logs and error messages"
        }
        return recommendations.get(category, "Review and fix security issue")

    def _get_cwe_id(self, category: str) -> str:
        """Get CWE ID for vulnerability category"""
        cwe_map = {
            'auth_bypass': "CWE-287",
            'injection': "CWE-94",
            'crypto_issues': "CWE-338",
            'deserialization': "CWE-502",
            'input_validation': "CWE-20",
            'race_conditions': "CWE-362",
            'info_disclosure': "CWE-200"
        }
        return cwe_map.get(category, "CWE-Other")

    def _generate_report(self) -> Dict:
        """Generate comprehensive security analysis report"""
        
        # Sort findings by severity
        severity_order = {'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
        self.findings.sort(key=lambda x: severity_order.get(x.severity, 4))
        
        # Generate statistics
        stats = {
            'total_findings': len(self.findings),
            'critical': len([f for f in self.findings if f.severity == 'CRITICAL']),
            'high': len([f for f in self.findings if f.severity == 'HIGH']),
            'medium': len([f for f in self.findings if f.severity == 'MEDIUM']),
            'low': len([f for f in self.findings if f.severity == 'LOW']),
            'files_analyzed': len(self.analyzed_files),
            'analysis_date': datetime.now().isoformat()
        }
        
        return {
            'summary': stats,
            'findings': [asdict(f) for f in self.findings],
            'analyzed_files': list(self.analyzed_files)
        }

    def _save_findings(self):
        """Save findings to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mozilla_autopush_security_analysis_{timestamp}.json"
        
        report = self._generate_report()
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"💾 Security analysis saved to: {filename}")

    def print_summary(self):
        """Print analysis summary"""
        if not self.findings:
            print("✅ No security issues detected")
            return
        
        print(f"\n🚨 MOZILLA AUTOPUSH-RS SECURITY ANALYSIS SUMMARY")
        print(f"=" * 60)
        
        stats = {}
        for finding in self.findings:
            stats[finding.severity] = stats.get(finding.severity, 0) + 1
        
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            count = stats.get(severity, 0)
            if count > 0:
                print(f"🔴 {severity}: {count} findings" if severity == 'CRITICAL' else
                      f"🟠 {severity}: {count} findings" if severity == 'HIGH' else
                      f"🟡 {severity}: {count} findings" if severity == 'MEDIUM' else
                      f"🔵 {severity}: {count} findings")
        
        print(f"\n📊 Total: {len(self.findings)} potential security issues")
        print(f"📁 Files analyzed: {len(self.analyzed_files)}")

if __name__ == "__main__":
    analyzer = MozillaAutopushSecurityAnalyzer()
    report = analyzer.analyze_codebase()
    analyzer.print_summary()
