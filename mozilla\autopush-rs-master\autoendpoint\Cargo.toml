[package]
name = "autoendpoint"
version.workspace = true
authors.workspace = true
edition.workspace = true

[dependencies]
a2 =  "0.10"
actix-cors.workspace = true
actix-http.workspace = true
actix-rt.workspace = true
actix-web.workspace = true
async-trait = "0.1"
backtrace.workspace = true
base64.workspace = true
cadence.workspace = true
chrono.workspace = true
config.workspace = true
docopt.workspace = true
fernet.workspace = true
futures.workspace = true
hex.workspace = true
jsonwebtoken = "9.3.0"
lazy_static.workspace = true
openssl.workspace = true
regex.workspace = true
reqwest.workspace = true
sentry.workspace = true
serde.workspace = true
serde_json.workspace = true
slog-scope.workspace = true
thiserror.workspace = true
tokio.workspace = true
url.workspace = true
uuid.workspace = true
validator = "0.20"
validator_derive = "0.20"
yup-oauth2 = "8.1"

autopush_common = { path = "../autopush-common" }
# Updating to 9+ requires configuring rust-tls (See https://github.com/dermesser/yup-oauth2/issues/235) <- this ticket is resolved, update
# yup-oauth2 = { version = "10.0.1", features = ["hyper-rustls"] }

# For mockito test debugging
#ureq={ version="2.4", features=["json"] }

[dev-dependencies]
deadpool = { workspace = true }
mockall.workspace = true
mockito = "1.4"
tempfile = "3.2.0"
tokio = { workspace = true, features = ["fs", "macros"] }

[features]
default = ["bigtable", "reliable_report"]
# data store types
bigtable = ["autopush_common/bigtable"]

# enable emulator to call locally run data store.
emulator = ["bigtable"]

# Enable "stub" router for local testing purposes.
# The "stub" will return specified error strings or success
# depending on which `app_id` client is called based on the registration
# and endpoint. See [autoendpoint::router::stub::router::StubRouter]
stub = []
# Verbosely log vapid assertions (NOT ADVISED FOR WIDE PRODUCTION USE)
log_vapid = []

reliable_report = ["autopush_common/reliable_report"]

[lints]
workspace = true
