import { apiGet, apiPost, apiDelete, API_ENDPOINTS, buildQueryParams, uploadFile } from './config';
import { Document, DocumentUploadData, ApiResponse, DocumentCategory } from '../types';

/**
 * Documents API service
 */
export const documentsApi = {
  /**
   * Get all documents with optional filtering and pagination
   */
  getDocuments: async (params?: {
    page?: number;
    limit?: number;
    projectId?: string;
    category?: DocumentCategory[];
    search?: string;
  }): Promise<ApiResponse<Document>> => {
    try {
      const queryString = params ? buildQueryParams(params) : '';
      const url = `${API_ENDPOINTS.DOCUMENTS.BASE}${queryString ? `?${queryString}` : ''}`;
      
      return await apiGet<ApiResponse<Document>>(url);
    } catch (error) {
      console.error('Error fetching documents:', error);
      throw error;
    }
  },

  /**
   * Get documents for a specific project
   */
  getProjectDocuments: async (
    projectId: string,
    params?: {
      page?: number;
      limit?: number;
      category?: DocumentCategory[];
    }
  ): Promise<ApiResponse<Document>> => {
    try {
      const queryParams = {
        ...params,
        where: {
          project: { equals: projectId },
        },
      };
      
      const queryString = buildQueryParams(queryParams);
      const url = `${API_ENDPOINTS.DOCUMENTS.BASE}?${queryString}`;
      
      return await apiGet<ApiResponse<Document>>(url);
    } catch (error) {
      console.error(`Error fetching documents for project ${projectId}:`, error);
      throw error;
    }
  },

  /**
   * Get a single document by ID
   */
  getDocument: async (id: string): Promise<Document> => {
    try {
      const response = await apiGet<Document>(API_ENDPOINTS.DOCUMENTS.BY_ID(id));
      return response;
    } catch (error) {
      console.error(`Error fetching document ${id}:`, error);
      throw error;
    }
  },

  /**
   * Upload a new document
   */
  uploadDocument: async (
    uploadData: DocumentUploadData,
    onProgress?: (progress: number) => void
  ): Promise<Document> => {
    try {
      // First, upload the file to get the media object
      const mediaResponse = await uploadFile(uploadData.file, onProgress);
      
      // Then create the document record
      const documentData = {
        filename: mediaResponse.filename,
        originalName: uploadData.file.name,
        mimeType: uploadData.file.type,
        filesize: uploadData.file.size,
        url: mediaResponse.url,
        alt: uploadData.alt || uploadData.file.name,
        project: uploadData.projectId,
        category: uploadData.category,
        version: 1,
        isLatest: true,
      };
      
      const response = await apiPost<Document>(API_ENDPOINTS.DOCUMENTS.BASE, documentData);
      return response;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  },

  /**
   * Upload multiple documents
   */
  uploadMultipleDocuments: async (
    uploads: DocumentUploadData[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<Document[]> => {
    try {
      const results: Document[] = [];
      
      for (let i = 0; i < uploads.length; i++) {
        const upload = uploads[i];
        const progressCallback = onProgress 
          ? (progress: number) => onProgress(i, progress)
          : undefined;
        
        const document = await documentsApi.uploadDocument(upload, progressCallback);
        results.push(document);
      }
      
      return results;
    } catch (error) {
      console.error('Error uploading multiple documents:', error);
      throw error;
    }
  },

  /**
   * Delete a document
   */
  deleteDocument: async (id: string): Promise<void> => {
    try {
      await apiDelete(API_ENDPOINTS.DOCUMENTS.BY_ID(id));
    } catch (error) {
      console.error(`Error deleting document ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get document download URL
   */
  getDownloadUrl: async (id: string): Promise<string> => {
    try {
      const document = await documentsApi.getDocument(id);
      return document.url;
    } catch (error) {
      console.error(`Error getting download URL for document ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get document statistics
   */
  getDocumentStats: async (projectId?: string): Promise<{
    total: number;
    byCategory: Record<DocumentCategory, number>;
    totalSize: number;
  }> => {
    try {
      const params = projectId ? { projectId, limit: 1000 } : { limit: 1000 };
      const response = await documentsApi.getDocuments(params);
      
      const documents = response.docs || [];
      
      const byCategory: Record<DocumentCategory, number> = {
        drawing: 0,
        specification: 0,
        contract: 0,
        photo: 0,
        report: 0,
        other: 0,
      };
      
      let totalSize = 0;
      
      documents.forEach(doc => {
        byCategory[doc.category] = (byCategory[doc.category] || 0) + 1;
        totalSize += doc.filesize || 0;
      });
      
      return {
        total: documents.length,
        byCategory,
        totalSize,
      };
    } catch (error) {
      console.error('Error fetching document stats:', error);
      throw error;
    }
  },
};

/**
 * Helper functions for document data
 */
export const documentHelpers = {
  /**
   * Format file size for display
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Get file type icon
   */
  getFileIcon: (mimeType: string): string => {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('dwg') || mimeType.includes('autocad')) return '📐';
    return '📎';
  },

  /**
   * Check if file is an image
   */
  isImage: (mimeType: string): boolean => {
    return mimeType.startsWith('image/');
  },

  /**
   * Check if file is a PDF
   */
  isPdf: (mimeType: string): boolean => {
    return mimeType.includes('pdf');
  },

  /**
   * Format document category for display
   */
  formatCategory: (category: DocumentCategory): string => {
    switch (category) {
      case 'drawing':
        return 'Drawing';
      case 'specification':
        return 'Specification';
      case 'contract':
        return 'Contract';
      case 'photo':
        return 'Photo';
      case 'report':
        return 'Report';
      case 'other':
        return 'Other';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  },

  /**
   * Get category color for UI
   */
  getCategoryColor: (category: DocumentCategory): string => {
    switch (category) {
      case 'drawing':
        return 'bg-blue-100 text-blue-800';
      case 'specification':
        return 'bg-green-100 text-green-800';
      case 'contract':
        return 'bg-purple-100 text-purple-800';
      case 'photo':
        return 'bg-pink-100 text-pink-800';
      case 'report':
        return 'bg-orange-100 text-orange-800';
      case 'other':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  /**
   * Validate file type
   */
  isValidFileType: (file: File): boolean => {
    const allowedTypes = process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || [
      'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'dwg'
    ];
    
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    return fileExtension ? allowedTypes.includes(fileExtension) : false;
  },

  /**
   * Validate file size
   */
  isValidFileSize: (file: File): boolean => {
    const maxSize = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'); // 10MB default
    return file.size <= maxSize;
  },
};
