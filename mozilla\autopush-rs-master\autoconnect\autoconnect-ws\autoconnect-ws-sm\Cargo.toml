[package]
name = "autoconnect_ws_sm"
version.workspace = true
edition.workspace = true
authors.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-web.workspace = true
actix-ws.workspace = true
backtrace.workspace = true
cadence.workspace = true
futures.workspace = true
reqwest.workspace = true
sentry.workspace = true
slog-scope.workspace = true
uuid.workspace = true
thiserror.workspace = true

autoconnect_common.workspace = true
autoconnect_settings.workspace = true
autopush_common.workspace = true

[dev-dependencies]
actix-rt.workspace = true
ctor.workspace = true
mockall.workspace = true
tokio.workspace = true
serde_json.workspace = true

autoconnect_common = { workspace = true, features = ["test-support"] }

[features]
reliable_report = []

[lints]
workspace = true
