[tool.black]
line-length = 99

[tool.isort]
profile = "black"
skip_gitignore = true

[tool.bandit]
# skips asserts
# B101: https://bandit.readthedocs.io/en/latest/plugins/b101_assert_used.html#
# B104: https://bandit.readthedocs.io/en/latest/plugins/b104_hardcoded_bind_all_interfaces.html
# B113: https://bandit.readthedocs.io/en/latest/plugins/b113_request_without_timeout.html
# B311: https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b311-random
# B404: https://bandit.readthedocs.io/en/latest/blacklists/blacklist_imports.html#b404-import-subprocess
skips = ["B101", "B104", "B113", "B311", "B404"]

[tool.mypy]
python_version = "3.12"
disable_error_code = "attr-defined"
disallow_untyped_calls = false
follow_imports = "normal"
ignore_missing_imports = true
pretty = true
show_error_codes = true
strict_optional = true
warn_no_return = true
warn_redundant_casts = true
warn_return_any = true
warn_unused_ignores = true
warn_unreachable = true

[tool.pydocstyle]
match = ".*\\.py"
convention = "pep257"
# Error Code Ref: https://www.pydocstyle.org/en/stable/error_codes.html
# D212 Multi-line docstring summary should start at the first line
add-select = ["D212"]
# D105 Docstrings for magic methods
# D107 Docstrings for __init__
# D203 as it conflicts with D211 https://github.com/PyCQA/pydocstyle/issues/141
# D205 1 blank line required between summary line and description, awkward spacing
# D400 First line should end with a period, doesn't work when sentence spans 2 lines
add-ignore = ["D105", "D107", "D203", "D205", "D400"]

[tool.poetry]
name = "tests"
version = "0.1.0"
description = "Autopush test solutions"
authors = ["Mozilla"]
license = "Mozilla Public License Version 2.0"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
websocket-client = "^1.8.0"
websockets = "^12.0"

[tool.poetry.group.dev.dependencies]
black = "^24.10.0"
isort = "^5.13.2"
# flake8 configs in .flake8 file. Doesn't support .toml
flake8 = "^6.1.0"
mypy = "^1.13.0"
pydocstyle = "^6.3.0"
bandit = "^1.7.10"


[tool.poetry.group.integration.dependencies]
cryptography = "^43.0.3"
jws = "^0.1.3"
psutil = "^5.9.8"
pytest = "^8.3.3"
pytest-asyncio = "^0.23.8"
pytest-order = "^1.3.0"
python-jose = "^3.3.0"
httpx = "^0.27.2"
fastapi = "^0.111.1"
uvicorn = { extras = ["standard"], version = "^0.29.0" }

[tool.poetry.group.load.dependencies]
locust = "^2.32.1"
numpy = "^1.26.4"
pydantic = "^2.9.2"
py-vapid = "^1.9"

[tool.poetry.group.notification.dependencies]
imgcompare = "^2.0.1"
pyscreenshot = "^3.1"
pytest = "^8.3.3"
PyVirtualDisplay = "^3.0"
pillow = "^11.0.0"
pytest-selenium = "^4.1.0"

[build-system]
requires = ["poetry-core>=1.9.1"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
addopts = "-m 'not stub and not sentry and not reliable_report'"
asyncio_mode = "auto"
faulthandler_timeout = 99999
log_cli = 1
# 10 DEBUG
# 20 INFO
log_cli_level = 10
# Pytest marker documentation: https://docs.pytest.org/en/7.1.x/example/markers.html
markers = [
    "stub: mark a test for the stub system",
    "sentry: mark a test for the sentry integration",
    "reliable_report: enable tests for reliability reporting",
]
