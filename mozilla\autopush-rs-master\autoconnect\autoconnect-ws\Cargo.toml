[package]
name = "autoconnect_ws"
version.workspace = true
edition.workspace = true
authors.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-http.workspace = true
actix-rt.workspace = true
actix-web.workspace = true
actix-ws.workspace = true
backtrace.workspace = true
futures.workspace = true
mockall.workspace = true
serde_json.workspace = true
sentry.workspace = true
slog-scope.workspace = true
strum.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["macros"] }

async-trait = "0.1"

autoconnect_common.workspace = true
autoconnect_settings.workspace = true
autoconnect_ws_sm = { path = "./autoconnect-ws-sm" }
autopush_common.workspace = true

[dev-dependencies]
async-stream = "0.3"
ctor.workspace = true

autoconnect_common = { workspace = true, features = ["test-support"] }

[features]
reliable_report = [
    "autopush_common/reliable_report",
    "autoconnect_ws_sm/reliable_report",
]
