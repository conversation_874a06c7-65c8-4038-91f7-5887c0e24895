import Cookies from 'js-cookie';

const TOKEN_KEY = 'construction_portal_token';
const TOKEN_EXPIRY_KEY = 'construction_portal_token_expiry';

/**
 * Store JWT token securely
 */
export const setToken = (token: string, expiryTimestamp?: number): void => {
  try {
    // Store in httpOnly cookie for security (fallback to localStorage in development)
    if (process.env.NODE_ENV === 'production') {
      Cookies.set(TOKEN_KEY, token, {
        expires: 7, // 7 days
        secure: true,
        sameSite: 'strict',
        httpOnly: false, // Note: httpOnly cookies can't be accessed by J<PERSON>, but we need access for API calls
      });
    } else {
      // Development: use localStorage for easier debugging
      localStorage.setItem(TOKEN_KEY, token);
    }

    // Store expiry timestamp
    if (expiryTimestamp) {
      const expiryDate = new Date(expiryTimestamp * 1000);
      Cookies.set(TOKEN_EXPIRY_KEY, expiryTimestamp.toString(), {
        expires: expiryDate,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
      });
    }
  } catch (error) {
    console.error('Error storing token:', error);
  }
};

/**
 * Retrieve JWT token
 */
export const getToken = (): string | null => {
  try {
    if (typeof window === 'undefined') {
      return null; // Server-side rendering
    }

    let token: string | undefined;

    if (process.env.NODE_ENV === 'production') {
      token = Cookies.get(TOKEN_KEY);
    } else {
      token = localStorage.getItem(TOKEN_KEY) || undefined;
    }

    if (!token) {
      return null;
    }

    // Check if token is expired
    if (isTokenExpired()) {
      removeToken();
      return null;
    }

    return token;
  } catch (error) {
    console.error('Error retrieving token:', error);
    return null;
  }
};

/**
 * Remove JWT token
 */
export const removeToken = (): void => {
  try {
    if (typeof window === 'undefined') {
      return; // Server-side rendering
    }

    // Remove from both storage methods
    Cookies.remove(TOKEN_KEY);
    Cookies.remove(TOKEN_EXPIRY_KEY);
    localStorage.removeItem(TOKEN_KEY);
  } catch (error) {
    console.error('Error removing token:', error);
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (): boolean => {
  try {
    const expiryTimestamp = Cookies.get(TOKEN_EXPIRY_KEY);
    
    if (!expiryTimestamp) {
      return false; // If no expiry is set, assume token is valid
    }

    const expiryTime = parseInt(expiryTimestamp, 10) * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    return currentTime >= expiryTime;
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return true; // Assume expired on error
  }
};

/**
 * Get token expiry time
 */
export const getTokenExpiry = (): Date | null => {
  try {
    const expiryTimestamp = Cookies.get(TOKEN_EXPIRY_KEY);
    
    if (!expiryTimestamp) {
      return null;
    }

    return new Date(parseInt(expiryTimestamp, 10) * 1000);
  } catch (error) {
    console.error('Error getting token expiry:', error);
    return null;
  }
};

/**
 * Decode JWT token payload (without verification)
 * Note: This is for client-side use only, never trust this data on the server
 */
export const decodeToken = (token: string): any => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Get user info from token
 */
export const getUserFromToken = (): any => {
  const token = getToken();
  if (!token) {
    return null;
  }

  const decoded = decodeToken(token);
  return decoded?.user || decoded || null;
};

/**
 * Check if user has specific role
 */
export const hasRole = (requiredRole: string): boolean => {
  const user = getUserFromToken();
  return user?.role === requiredRole;
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (roles: string[]): boolean => {
  const user = getUserFromToken();
  return roles.includes(user?.role);
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();
  return !!token && !isTokenExpired();
};
