 Backend Integration Guide for <PERSON>

## 🎯 **Overview: What Needs Backend Integration**

The frontend is 100% complete and functional with mock data. <PERSON> needs to integrate these 4 backend components:

```
Backend Requirements:
├── 🗄️ MongoDB Database (Data Storage)
├── 🚀 Payload CMS (Content Management)
├── ☁️ Real File Storage (AWS S3/CloudFlare R2)
└── 🔐 Production Authentication (JWT + Security)
```

---

## 🗄️ **1. MongoDB Database Integration**

### **Step 1: Database Setup**
```bash
# Install MongoDB dependencies
npm install mongodb mongoose

# Or for Payload CMS integration
npm install @payloadcms/db-mongodb
```

### **Step 2: Database Schema Design**

#### **Users Collection:**
```javascript
// models/User.js
const userSchema = {
  _id: ObjectId,
  email: String, // unique, required
  password: String, // hashed with bcrypt
  firstName: String,
  lastName: String,
  role: String, // 'admin', 'project_manager', 'client'
  company: String,
  phone: String,
  avatar: String, // URL to profile image
  isActive: Boolean,
  projects: [ObjectId], // Array of assigned project IDs
  createdAt: Date,
  updatedAt: Date
}
```

#### **Projects Collection:**
```javascript
// models/Project.js
const projectSchema = {
  _id: ObjectId,
  name: String,
  description: String,
  status: String, // 'planning', 'in_progress', 'on_hold', 'completed'
  progress: Number, // 0-100
  startDate: Date,
  endDate: Date,
  location: String,
  budget: Number,
  client: ObjectId, // Reference to Users collection
  projectManager: ObjectId, // Reference to Users collection
  teamMembers: [ObjectId], // Array of User references
  documents: [ObjectId], // Array of Document references
  createdAt: Date,
  updatedAt: Date
}
```

#### **Documents Collection:**
```javascript
// models/Document.js
const documentSchema = {
  _id: ObjectId,
  filename: String, // Generated filename
  originalName: String, // User's original filename
  mimeType: String,
  filesize: Number,
  url: String, // S3/CloudFlare URL
  category: String, // 'drawing', 'specification', 'photo', 'contract', 'report', 'other'
  project: ObjectId, // Reference to Projects collection
  uploadedBy: ObjectId, // Reference to Users collection
  version: Number,
  isLatest: Boolean,
  tags: [String],
  createdAt: Date,
  updatedAt: Date
}
```

### **Step 3: Database Connection**
```javascript
// lib/mongodb.js
import { MongoClient } from 'mongodb';

const uri = process.env.MONGODB_URI;
let client;
let clientPromise;

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri);
  clientPromise = client.connect();
}

export default clientPromise;
```

---

## 🚀 **2. Payload CMS Integration**

### **Step 1: Install Payload CMS**
```bash
npm install payload @payloadcms/bundler-webpack @payloadcms/db-mongodb @payloadcms/richtext-slate
```

### **Step 2: Payload Configuration**
```javascript
// payload.config.ts
import { buildConfig } from 'payload/config';
import { mongooseAdapter } from '@payloadcms/db-mongodb';
import { webpackBundler } from '@payloadcms/bundler-webpack';
import { slateEditor } from '@payloadcms/richtext-slate';

export default buildConfig({
  admin: {
    user: 'users',
    bundler: webpackBundler(),
    meta: {
      titleSuffix: '- Builders By Design',
      favicon: '/favicon.ico',
    },
  },
  editor: slateEditor({}),
  db: mongooseAdapter({
    url: process.env.DATABASE_URI,
  }),
  collections: [
    // Users Collection
    {
      slug: 'users',
      auth: {
        tokenExpiration: 7200, // 2 hours
      },
      admin: {
        useAsTitle: 'email',
      },
      fields: [
        {
          name: 'firstName',
          type: 'text',
          required: true,
        },
        {
          name: 'lastName',
          type: 'text',
          required: true,
        },
        {
          name: 'role',
          type: 'select',
          required: true,
          options: [
            { label: 'Admin', value: 'admin' },
            { label: 'Project Manager', value: 'project_manager' },
            { label: 'Client', value: 'client' },
          ],
        },
        {
          name: 'company',
          type: 'text',
        },
        {
          name: 'phone',
          type: 'text',
        },
        {
          name: 'avatar',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'projects',
          type: 'relationship',
          relationTo: 'projects',
          hasMany: true,
        },
      ],
    },
    
    // Projects Collection
    {
      slug: 'projects',
      admin: {
        useAsTitle: 'name',
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'status',
          type: 'select',
          required: true,
          options: [
            { label: 'Planning', value: 'planning' },
            { label: 'In Progress', value: 'in_progress' },
            { label: 'On Hold', value: 'on_hold' },
            { label: 'Completed', value: 'completed' },
          ],
        },
        {
          name: 'progress',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 0,
        },
        {
          name: 'startDate',
          type: 'date',
        },
        {
          name: 'endDate',
          type: 'date',
        },
        {
          name: 'location',
          type: 'text',
        },
        {
          name: 'budget',
          type: 'number',
        },
        {
          name: 'client',
          type: 'relationship',
          relationTo: 'users',
          required: true,
        },
        {
          name: 'projectManager',
          type: 'relationship',
          relationTo: 'users',
        },
        {
          name: 'teamMembers',
          type: 'relationship',
          relationTo: 'users',
          hasMany: true,
        },
      ],
    },
    
    // Documents Collection
    {
      slug: 'documents',
      upload: {
        staticURL: '/documents',
        staticDir: 'documents',
        mimeTypes: ['image/*', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      },
      fields: [
        {
          name: 'category',
          type: 'select',
          required: true,
          options: [
            { label: 'Drawing', value: 'drawing' },
            { label: 'Specification', value: 'specification' },
            { label: 'Photo', value: 'photo' },
            { label: 'Contract', value: 'contract' },
            { label: 'Report', value: 'report' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'project',
          type: 'relationship',
          relationTo: 'projects',
          required: true,
        },
        {
          name: 'version',
          type: 'number',
          defaultValue: 1,
        },
        {
          name: 'isLatest',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'tags',
          type: 'text',
          hasMany: true,
        },
      ],
    },
  ],
});
```

### **Step 3: Payload API Integration**
```javascript
// lib/payload.js
import payload from 'payload';

export const getPayload = async () => {
  if (!payload.isInitialized) {
    await payload.init({
      secret: process.env.PAYLOAD_SECRET,
      mongoURL: process.env.DATABASE_URI,
      express: app,
    });
  }
  return payload;
};
```

---

## ☁️ **3. Real File Storage Integration**

### **Option A: AWS S3 Setup**
```bash
npm install aws-sdk @aws-sdk/client-s3
```

```javascript
// lib/s3.js
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

export const uploadFile = async (file, key) => {
  const command = new PutObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key,
    Body: file,
    ContentType: file.type,
  });

  const result = await s3Client.send(command);
  return {
    url: `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`,
    key: key,
  };
};

export const deleteFile = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key,
  });

  return s3Client.send(command);
};

export const getSignedDownloadUrl = async (key) => {
  const command = new GetObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key,
  });

  return getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour
};
```

### **Option B: CloudFlare R2 Setup**
```bash
npm install @aws-sdk/client-s3
```

```javascript
// lib/r2.js
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  },
});

export const uploadToR2 = async (file, key) => {
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME,
    Key: key,
    Body: file,
    ContentType: file.type,
  });

  const result = await r2Client.send(command);
  return {
    url: `https://${process.env.R2_PUBLIC_URL}/${key}`,
    key: key,
  };
};

export const deleteFromR2 = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME,
    Key: key,
  });

  return r2Client.send(command);
};
```

---

## 🔐 **4. Production Authentication**

### **Step 1: Install Authentication Dependencies**
```bash
npm install jsonwebtoken bcryptjs
npm install --save-dev @types/jsonwebtoken @types/bcryptjs
```

### **Step 2: Authentication Utilities**
```javascript
// lib/auth.js
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

export const hashPassword = async (password) => {
  return bcrypt.hash(password, 12);
};

export const verifyPassword = async (password, hashedPassword) => {
  return bcrypt.compare(password, hashedPassword);
};

export const generateToken = (user) => {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

export const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

export const generateRefreshToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '30d' }
  );
};
```

---

## 🔧 **Environment Variables Setup**

### **Required .env.local:**
```bash
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/builders-by-design
DATABASE_URI=mongodb+srv://username:<EMAIL>/builders-by-design

# Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_REFRESH_SECRET=your-refresh-token-secret-key
NEXTAUTH_SECRET=your-nextauth-secret-for-additional-security

# Payload CMS
PAYLOAD_SECRET=your-payload-cms-secret-key

# File Storage - AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=builders-by-design-documents

# File Storage - CloudFlare R2 (Alternative)
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_BUCKET_NAME=builders-by-design-docs
R2_PUBLIC_URL=https://your-r2-public-url.com

# App Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_MAX_FILE_SIZE=********
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx,dwg

# Production URLs (for deployment)
NEXTAUTH_URL=https://your-domain.com
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

---

## 📋 **Integration Checklist for James**

### **Phase 1: Database & CMS (Week 1)**
- [ ] Set up MongoDB Atlas cluster
- [ ] Install and configure Payload CMS
- [ ] Create database collections
- [ ] Test Payload admin panel
- [ ] Import sample data

### **Phase 2: Authentication (Week 2)**
- [ ] Replace mock auth APIs with real JWT
- [ ] Implement password hashing
- [ ] Add middleware protection
- [ ] Test login/logout flow
- [ ] Add user registration (if needed)

### **Phase 3: File Storage (Week 3)**
- [ ] Choose AWS S3 or CloudFlare R2
- [ ] Set up storage bucket/container
- [ ] Implement file upload API
- [ ] Add file deletion functionality
- [ ] Test file operations

### **Phase 4: API Integration (Week 4)**
- [ ] Replace all mock API routes
- [ ] Implement role-based data filtering
- [ ] Add data validation
- [ ] Test all CRUD operations
- [ ] Add error handling

### **Phase 5: Security & Testing (Week 5)**
- [ ] Add rate limiting
- [ ] Implement CORS policies
- [ ] Security audit
- [ ] End-to-end testing
- [ ] Performance optimization

### **Phase 6: Deployment (Week 6)**
- [ ] Production environment setup
- [ ] Database migration
- [ ] File storage configuration
- [ ] SSL certificates
- [ ] User acceptance testing

---

## 🚀 **Quick Start Commands for James**

```bash
# 1. Install additional dependencies
npm install payload @payloadcms/db-mongodb @payloadcms/bundler-webpack
npm install mongodb mongoose jsonwebtoken bcryptjs
npm install aws-sdk @aws-sdk/client-s3

# 2. Set up environment variables
cp .env.example .env.local
# Edit .env.local with your values

# 3. Initialize Payload CMS
npx create-payload-app@latest --template blank

# 4. Start development
npm run dev

# 5. Access Payload admin
# http://localhost:3000/admin
```

**The frontend is 100% ready - James just needs to connect these backend services to make it fully production-ready!** 🎯
