apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: locust-master
  labels:
    name: locust-master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: locust-master
  template:
    metadata:
      labels:
        app: locust-master
    spec:
      containers:
        - name: locust-master
          image: us-central1-docker.pkg.dev/[PROJECT_ID]/autopush/locust-autopush:[LOCUST_IMAGE_TAG]
          env:
            - name: LOCUST_MODE_MASTER
              value: "true"
            - name: LOCUST_USERCLASS_PICKER
              value: "true"
            - name: LOCUST_MODERN_UI
              value: "true"
            - name: TARGET_HOST
              value:
            - name: LOCUST_CSV
              value:
            - name: LOCUST_HOST
              value:
            - name: LOCUST_LOGLEVEL
              value:
            - name: LOCUST_LOGFILE
              value:
          ports:
            - name: loc-master-web
              containerPort: 8089
              protocol: TCP
            - name: loc-master-p1
              containerPort: 5557
              protocol: TCP
            - name: loc-master-p2
              containerPort: 5558
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 3Gi
            requests:
              cpu: 1
              memory: 2Gi
