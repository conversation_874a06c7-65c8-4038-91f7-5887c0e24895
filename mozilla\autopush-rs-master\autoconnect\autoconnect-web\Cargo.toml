[package]
name = "autoconnect_web"
authors.workspace = true
edition.workspace = true
version.workspace = true

[dependencies]
actix-http.workspace = true
actix-rt.workspace = true
actix-web.workspace = true
futures-util.workspace = true
serde_json.workspace = true
slog-scope.workspace = true
thiserror.workspace = true
uuid.workspace = true

autoconnect_common.workspace = true
autoconnect_settings.workspace = true
autoconnect_ws.workspace = true
autopush_common.workspace = true

[dev-dependencies]
actix-codec = "0.5"
actix-http.workspace = true
actix-test.workspace = true
ctor.workspace = true
tokio.workspace = true

autoconnect_common = { workspace = true, features = ["test-support"] }

[features]
reliable_report = [
    "autopush_common/reliable_report",
    "autoconnect_ws/reliable_report",
]
