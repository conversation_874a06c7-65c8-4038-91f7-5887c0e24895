[package]
name = "autoconnect_common"
authors.workspace = true
edition.workspace = true
version.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-web.workspace = true
bytestring.workspace = true
cadence.workspace = true
futures.workspace = true
futures-locks.workspace = true
hyper.workspace = true
reqwest.workspace = true
tokio.workspace = true
sentry.workspace = true
serde.workspace = true
serde_derive.workspace = true
serde_json.workspace = true
slog-scope.workspace = true
strum.workspace = true
strum_macros.workspace = true
uuid.workspace = true

autopush_common.workspace = true

[features]
test-support = []
reliable_report = ["autopush_common/reliable_report"]
