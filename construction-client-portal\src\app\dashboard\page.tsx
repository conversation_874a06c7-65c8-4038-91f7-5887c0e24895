'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { DashboardStatsSkeleton, ProjectCardSkeleton } from '@/components/ui/loading';
import { ErrorMessage } from '@/components/ui/error';
import { 
  FolderOpen, 
  FileText, 
  Users, 
  TrendingUp,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  ArrowRight
} from 'lucide-react';
import { getUserDisplayName, formatUserRole } from '@/lib/api/auth';
import { projectHelpers } from '@/lib/api/projects';
import Link from 'next/link';

// Mock data - in real app, this would come from API
const mockStats = {
  totalProjects: 12,
  activeProjects: 8,
  completedProjects: 4,
  totalDocuments: 156,
};

const mockRecentProjects = [
  {
    id: '1',
    name: 'Downtown Office Complex',
    status: 'in_progress',
    progress: 65,
    client: { firstName: 'John', lastName: 'Smith', avatar: null },
    endDate: '2024-03-15',
    documentsCount: 23,
  },
  {
    id: '2',
    name: 'Residential Tower A',
    status: 'planning',
    progress: 15,
    client: { firstName: 'Sarah', lastName: 'Johnson', avatar: null },
    endDate: '2024-06-30',
    documentsCount: 8,
  },
  {
    id: '3',
    name: 'Shopping Mall Renovation',
    status: 'on_hold',
    progress: 45,
    client: { firstName: 'Mike', lastName: 'Davis', avatar: null },
    endDate: '2024-04-20',
    documentsCount: 31,
  },
];

const mockRecentActivity = [
  {
    id: '1',
    type: 'document_uploaded',
    message: 'New blueprint uploaded to Downtown Office Complex',
    user: { firstName: 'Alice', lastName: 'Wilson' },
    time: '2 hours ago',
  },
  {
    id: '2',
    type: 'project_updated',
    message: 'Project status updated for Residential Tower A',
    user: { firstName: 'Bob', lastName: 'Brown' },
    time: '4 hours ago',
  },
  {
    id: '3',
    type: 'user_added',
    message: 'New team member added to Shopping Mall project',
    user: { firstName: 'Carol', lastName: 'Taylor' },
    time: '1 day ago',
  },
];

export default function DashboardPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate API loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  if (error) {
    return (
      <DashboardLayout>
        <ErrorMessage message={error} />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {getGreeting()}, {user?.firstName}! 👋
            </h1>
            <p className="text-muted-foreground mt-1">
              Welcome to your Builders By Design client portal.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-sm">
              {formatUserRole(user?.role || '')}
            </Badge>
            <Button asChild>
              <Link href="/projects/new">
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {isLoading ? (
          <DashboardStatsSkeleton />
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.totalProjects}</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.activeProjects}</div>
                <p className="text-xs text-muted-foreground">
                  67% of total projects
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Documents</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.totalDocuments}</div>
                <p className="text-xs text-muted-foreground">
                  +12 this week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Team Members</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24</div>
                <p className="text-xs text-muted-foreground">
                  Across all projects
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Projects */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Projects</CardTitle>
                    <CardDescription>
                      Your most recently updated projects
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/projects">
                      View All
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <ProjectCardSkeleton key={i} />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {mockRecentProjects.map((project) => (
                      <div
                        key={project.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium">{project.name}</h4>
                            <Badge 
                              variant="secondary"
                              className={projectHelpers.getStatusColor(project.status)}
                            >
                              {projectHelpers.formatStatus(project.status)}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Avatar className="h-5 w-5">
                                <AvatarFallback className="text-xs">
                                  {project.client.firstName[0]}{project.client.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              <span>{project.client.firstName} {project.client.lastName}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>Due {new Date(project.endDate).toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <FileText className="h-4 w-4" />
                              <span>{project.documentsCount} docs</span>
                            </div>
                          </div>
                          <div className="mt-2">
                            <div className="flex items-center gap-2">
                              <Progress value={project.progress} className="flex-1" />
                              <span className="text-sm text-muted-foreground">
                                {project.progress}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest updates from your team
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRecentActivity.map((activity) => (
                    <div key={activity.id} className="flex gap-3">
                      <div className="flex-shrink-0">
                        {activity.type === 'document_uploaded' && (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                            <FileText className="h-4 w-4 text-blue-600" />
                          </div>
                        )}
                        {activity.type === 'project_updated' && (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                        )}
                        {activity.type === 'user_added' && (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100">
                            <Users className="h-4 w-4 text-purple-600" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{activity.message}</p>
                        <div className="flex items-center gap-1 mt-1">
                          <span className="text-xs text-muted-foreground">
                            by {activity.user.firstName} {activity.user.lastName}
                          </span>
                          <span className="text-xs text-muted-foreground">•</span>
                          <span className="text-xs text-muted-foreground">
                            {activity.time}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
