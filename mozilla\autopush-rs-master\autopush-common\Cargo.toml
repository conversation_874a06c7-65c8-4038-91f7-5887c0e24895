[package]
name = "autopush_common"
version.workspace = true
authors.workspace = true
edition.workspace = true
build = "build.rs"

[lib]
name = "autopush_common"

[dependencies]
actix-web.workspace = true
backtrace.workspace = true
base64.workspace = true
cadence.workspace = true
chrono.workspace = true
config.workspace = true
deadpool.workspace = true
fernet.workspace = true
futures.workspace = true
futures-util.workspace = true
lazy_static.workspace = true
mockall.workspace = true
openssl.workspace = true
regex.workspace = true
sentry-backtrace.workspace = true
sentry.workspace = true
serde.workspace = true
serde_derive.workspace = true
serde_json.workspace = true
slog.workspace = true
slog-async.workspace = true
slog-envlogger.workspace = true
slog-mozlog-json.workspace = true
slog-scope.workspace = true
slog-stdlog.workspace = true
slog-term.workspace = true
strum.workspace = true
strum_macros.workspace = true
thiserror.workspace = true
uuid.workspace = true
url.workspace = true

again = "0.1"
async-trait = "0.1"
derive_builder = "0.20"
gethostname = "1.0"
num_cpus = "1.16"
woothee = "0.13"

# #[cfg(bigtable)] for this section.
# the following three crates must match what is specifed in google-cloud-rust-raw's dependencies
google-cloud-rust-raw = { version = "0.16", default-features = false, features = [
    "bigtable",
], optional = true }
grpcio = { version = "=0.13.0", features = ["openssl"], optional = true }
grpcio-sys = { version = "=0.13.0", optional = true }
prometheus-client = { version = "0.23", optional = true }
protobuf = { version = "=2.28.0", optional = true } # grpcio does not support protobuf 3+
form_urlencoded = { version = "1.2", optional = true }

deadpool-redis = { version = "0.21", optional = true }
redis-module = { version = "2.0", optional = true }
redis = { version = "0.31", optional = true }

[dev-dependencies]
actix-rt = "2.8"
mockito = "0.31"                                     # mockito > 0.31 will require significant reworking of the tests and is not critical yet.
redis-test = { version = "0.11", features = ["aio"] }
tempfile = "3.2.0"
tokio = { workspace = true, features = ["macros"] }

[features]
# NOTE: Do not set a `default` here, rather specify them in the calling library.
# This is to reduce complexity around feature specification.

bigtable = [
    "dep:google-cloud-rust-raw",
    "dep:grpcio",
    "dep:grpcio-sys",
    "dep:protobuf",
    "dep:form_urlencoded",
]
emulator = [
    "bigtable",
] # used for testing big table, requires an external bigtable emulator running.
reliable_report = [
    "dep:redis",
    "dep:prometheus-client",
    "dep:redis-module",
    "dep:deadpool-redis",
]
