apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: locust-worker
  labels:
    name: locust-worker
spec:
  replicas: [WORKER_COUNT]
  selector:
    matchLabels:
      app: locust-worker
  template:
    metadata:
      labels:
        app: locust-worker
    spec:
      nodeSelector:
        node-pool: locust-workers
      containers:
        - name: locust-worker
          image: us-central1-docker.pkg.dev/[PROJECT_ID]/autopush/locust-autopush:[LOCUST_IMAGE_TAG]
          env:
            - name: LOCUST_MODE_WORKER
              value: "true"
            - name: LOCUST_MASTER_NODE_HOST
              value: locust-master
            - name: TARGET_HOST
              value:
            - name: LOCUST_LOGLEVEL
              value:
            - name: LOCUST_LOGFILE
              value:
            - name: AUTOPUSH_VAPID_KEY
              value:
          resources:
            # Forcing requests and limits to match to ensured pods run in Guaranteed QoS class
            # Using 1 core per worker based on recommendations from https://docs.locust.io/en/stable/running-distributed.html
            limits:
              cpu: 1
              memory: 3Gi
            requests:
              cpu: 1
              memory: 3Gi
