import { apiGet, apiPost, apiPut, apiDelete, API_ENDPOINTS, buildQueryParams } from './config';
import { Project, ProjectFormData, ApiResponse, SearchFilters } from '../types';

/**
 * Projects API service
 */
export const projectsApi = {
  /**
   * Get all projects with optional filtering and pagination
   */
  getProjects: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string[];
    clientId?: string;
    projectManagerId?: string;
  }): Promise<ApiResponse<Project>> => {
    try {
      const queryString = params ? buildQueryParams(params) : '';
      const url = `${API_ENDPOINTS.PROJECTS.BASE}${queryString ? `?${queryString}` : ''}`;
      
      return await apiGet<ApiResponse<Project>>(url);
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  },

  /**
   * Get a single project by ID
   */
  getProject: async (id: string): Promise<Project> => {
    try {
      const response = await apiGet<Project>(API_ENDPOINTS.PROJECTS.BY_ID(id));
      return response;
    } catch (error) {
      console.error(`Error fetching project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new project
   */
  createProject: async (data: ProjectFormData): Promise<Project> => {
    try {
      const response = await apiPost<Project>(API_ENDPOINTS.PROJECTS.BASE, data);
      return response;
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  },

  /**
   * Update an existing project
   */
  updateProject: async (id: string, data: Partial<ProjectFormData>): Promise<Project> => {
    try {
      const response = await apiPut<Project>(API_ENDPOINTS.PROJECTS.BY_ID(id), data);
      return response;
    } catch (error) {
      console.error(`Error updating project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a project
   */
  deleteProject: async (id: string): Promise<void> => {
    try {
      await apiDelete(API_ENDPOINTS.PROJECTS.BY_ID(id));
    } catch (error) {
      console.error(`Error deleting project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get projects assigned to current user
   */
  getMyProjects: async (params?: {
    page?: number;
    limit?: number;
    status?: string[];
  }): Promise<ApiResponse<Project>> => {
    try {
      const queryParams = {
        ...params,
        where: {
          or: [
            { 'client.id': { equals: 'current_user' } },
            { 'projectManager.id': { equals: 'current_user' } },
          ],
        },
      };
      
      const queryString = buildQueryParams(queryParams);
      const url = `${API_ENDPOINTS.PROJECTS.BASE}?${queryString}`;
      
      return await apiGet<ApiResponse<Project>>(url);
    } catch (error) {
      console.error('Error fetching my projects:', error);
      throw error;
    }
  },

  /**
   * Get project statistics
   */
  getProjectStats: async (): Promise<{
    total: number;
    active: number;
    completed: number;
    onHold: number;
    cancelled: number;
  }> => {
    try {
      // This would typically be a dedicated endpoint, but we'll aggregate from projects
      const response = await apiGet<ApiResponse<Project>>(
        `${API_ENDPOINTS.PROJECTS.BASE}?limit=1000`
      );
      
      const projects = response.docs || [];
      
      return {
        total: projects.length,
        active: projects.filter(p => p.status === 'in_progress').length,
        completed: projects.filter(p => p.status === 'completed').length,
        onHold: projects.filter(p => p.status === 'on_hold').length,
        cancelled: projects.filter(p => p.status === 'cancelled').length,
      };
    } catch (error) {
      console.error('Error fetching project stats:', error);
      throw error;
    }
  },

  /**
   * Search projects with advanced filters
   */
  searchProjects: async (filters: SearchFilters): Promise<ApiResponse<Project>> => {
    try {
      const queryParams: any = {};
      
      if (filters.query) {
        queryParams.search = filters.query;
      }
      
      if (filters.status && filters.status.length > 0) {
        queryParams.status = filters.status;
      }
      
      if (filters.dateRange) {
        queryParams.createdAt = {
          greater_than_equal: filters.dateRange.from,
          less_than_equal: filters.dateRange.to,
        };
      }
      
      if (filters.userId) {
        queryParams.where = {
          or: [
            { 'client.id': { equals: filters.userId } },
            { 'projectManager.id': { equals: filters.userId } },
          ],
        };
      }
      
      const queryString = buildQueryParams(queryParams);
      const url = `${API_ENDPOINTS.PROJECTS.BASE}?${queryString}`;
      
      return await apiGet<ApiResponse<Project>>(url);
    } catch (error) {
      console.error('Error searching projects:', error);
      throw error;
    }
  },
};

/**
 * Helper functions for project data
 */
export const projectHelpers = {
  /**
   * Format project status for display
   */
  formatStatus: (status: string): string => {
    switch (status) {
      case 'planning':
        return 'Planning';
      case 'in_progress':
        return 'In Progress';
      case 'on_hold':
        return 'On Hold';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  },

  /**
   * Get status color for UI
   */
  getStatusColor: (status: string): string => {
    switch (status) {
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-green-100 text-green-800';
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  /**
   * Calculate project progress percentage
   */
  calculateProgress: (project: Project): number => {
    if (project.status === 'completed') return 100;
    if (project.status === 'cancelled') return 0;
    
    // This would typically be based on tasks/milestones
    // For now, we'll use a simple date-based calculation
    if (!project.startDate || !project.endDate) return 0;
    
    const start = new Date(project.startDate).getTime();
    const end = new Date(project.endDate).getTime();
    const now = Date.now();
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    return Math.round(((now - start) / (end - start)) * 100);
  },

  /**
   * Check if project is overdue
   */
  isOverdue: (project: Project): boolean => {
    if (!project.endDate || project.status === 'completed' || project.status === 'cancelled') {
      return false;
    }
    
    return new Date(project.endDate) < new Date();
  },
};
