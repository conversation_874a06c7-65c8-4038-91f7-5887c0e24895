services:
  locust_master:
    image: locust
    build:
      context: ../../
      dockerfile: ./tests/load/Dockerfile
    container_name: locust_master
    ports:
      - "8089:8089"
    environment:
      LOCUST_USERCLASS_PICKER: true
      LOCUST_MODERN_UI: true
      LOCUST_HOST: ${LOCUST_HOST}
      LOCUST_LOGLEVEL: "INFO"
    command: >
      --master

  locust_worker:
    image: locust
    build:
      context: ../../
      dockerfile: ./tests/load/Dockerfile
    environment:
      LOCUST_MASTER_NODE_HOST: locust_master
      LOCUST_LOGLEVEL: "INFO"
    command: >
      --worker
