{"summary": {"total_findings": 146, "critical": 1, "high": 125, "medium": 12, "low": 8, "files_analyzed": 104, "analysis_date": "2025-08-05T00:34:59.997556"}, "findings": [{"severity": "CRITICAL", "category": "Auth Bypass", "title": "Potential Auth Bypass Issue", "description": "Pattern 'expect\\(.*auth.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\registration.rs", "line_number": 69, "code_snippet": ".expect(\"At least one auth key must be provided in the settings\");", "impact": "Complete authentication bypass allowing unauthorized access", "recommendation": "Implement proper authentication checks and error handling", "cwe_id": "CWE-287", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\endpoint.rs", "line_number": 36, "code_snippet": "let final_url = root.join(&format!(\"v2/{encrypted}\")).map_err(|e| {", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\endpoint.rs", "line_number": 37, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Encrypted endpoint data is not URL-safe {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\endpoint.rs", "line_number": 42, "code_snippet": "let final_url = root.join(&format!(\"v1/{encrypted}\")).map_err(|e| {", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\endpoint.rs", "line_number": 43, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Encrypted endpoint data is not URL-safe {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\logging.rs", "line_number": 14, "code_snippet": ".logger_name(format!(\"{name}-{version}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\logging.rs", "line_number": 15, "code_snippet": ".msg_type(format!(\"{name}:log\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\notification.rs", "line_number": 52, "code_snippet": "format!(\"{TOPIC_NOTIFICATION_PREFIX}:{chid}:{topic}\")", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\notification.rs", "line_number": 66, "code_snippet": "format!(\"{}:{}\", chid, self.version)", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 144, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Could not config reliability pool {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 150, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Could not build reliability pool {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 230, "code_snippet": "let state_key = format!(\"state.{id}\");", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 478, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Could not read report {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 494, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Could not ping reliability datastore: {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 556, "code_snippet": "ApcErrorKind::GeneralError(format!(\"Could not generate Reliability report {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 592, "code_snippet": "assert!(generated.contains(&format!(\"# TYPE {METRIC_NAME}\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 594, "code_snippet": "assert!(generated.contains(&format!(\"{METRIC_NAME}{{state=\\\"{recv}\\\"}} 111\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "line_number": 595, "code_snippet": "assert!(generated.contains(&format!(\"{METRIC_NAME}{{state=\\\"{trns}\\\"}} 444\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\sentry.rs", "line_number": 83, "code_snippet": "let dbg = format!(\"{err:?}\");", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "line_number": 70, "code_snippet": "format!(\"^{TOPIC_NOTIFICATION_PREFIX}:\\\\S+:\\\\S+$\").as_str(),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "line_number": 71, "code_snippet": "format!(\"^{STANDARD_NOTIFICATION_PREFIX}:\\\\d+:\\\\S+$\").as_str(),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "line_number": 139, "code_snippet": "let chidmessageid = format!(\"01:{}:mytopic\", chid.hyphenated());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "line_number": 150, "code_snippet": "let chidmessageid = format!(\"02:{}:{}\", sortkey_timestamp, chid.hyphenated());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\middleware\\sentry.rs", "line_number": 148, "code_snippet": "let label = format!(\"{label_prefix}.{label}\");", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\mod.rs", "line_number": 158, "code_snippet": ".map_err(|e| DbError::General(format!(\"Could not parse DdbSettings: {e:?}\")))?;", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'serde_json::from_str\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\mod.rs", "line_number": 157, "code_snippet": "let mut me: Self = serde_json::from_str(setting_string)", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\pool.rs", "line_number": 69, "code_snippet": ".map_err(|e| DbError::ConnectionError(format!(\"Invalid DSN: {endpoint:?} : {e:?}\")))?;", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\cell.rs", "line_number": 29, "code_snippet": "timestamp: SystemTime::now(),", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\error.rs", "line_number": 179, "code_snippet": "BigTableError::PoolTimeout(tt) => vec![(\"type\", format!(\"{tt:?}\").to_lowercase())],", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\merge.rs", "line_number": 46, "code_snippet": "timestamp: SystemTime::now(),", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\merge.rs", "line_number": 339, "code_snippet": "cell_in_progress.timestamp = SystemTime::now();", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\metadata.rs", "line_number": 120, "code_snippet": "format!(\"session={SESSION}&foo=bar+baz\")", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 106, "code_snippet": "let bt_now: i64 = SystemTime::now()", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 805, "code_snippet": "std::time::SystemTime::now() + Duration::from_secs(MAX_ROUTER_TTL.num_seconds() as u64);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1088, "code_snippet": "std::time::SystemTime::now() + Duration::from_secs(MAX_ROUTER_TTL.num_seconds() as u64);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1132, "code_snippet": "std::time::SystemTime::now() + Duration::from_secs(MAX_ROUTER_TTL.num_seconds() as u64);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1183, "code_snippet": "let expiry = SystemTime::now() + Duration::from_secs(message.ttl);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1304, "code_snippet": "std::time::SystemTime::now() + Duration::from_secs(MAX_ROUTER_TTL.num_seconds() as u64);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1487, "code_snippet": "let expiry = SystemTime::now() + Duration::from_secs(RELIABLE_LOG_TTL.num_seconds() as u64);", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1538, "code_snippet": "SystemTime::now()", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1685, "code_snippet": "SystemTime::now()", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'SystemTime::now\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1937, "code_snippet": "SystemTime::now()", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 156, "code_snippet": "family_filter(format!(\"^{ROUTER_FAMILY}$\")),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 246, "code_snippet": "qualifier: format!(\"chid:{}\", channel_id.as_hyphenated()),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 311, "code_snippet": "Some(&format!(\"{grpc_call_status:?}\")),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 744, "code_snippet": "format!(\"rows_to_notification expected chidmessageid: {e}\"),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 776, "code_snippet": "DbError::DeserializeString(format!(\"Could not parse reliable_state {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 940, "code_snippet": "row_key_filter.set_row_key_regex_filter(format!(\"^{}$\", row.row_key).into_bytes());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 988, "code_snippet": "filters.push(family_filter(format!(\"^{ROUTER_FAMILY}$\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1002, "code_snippet": "Some(format!(\"{row:#?}\")),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1032, "code_snippet": "DbError::Serialization(format!(\"Could not deserialize version: {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1040, "code_snippet": "DbError::Serialization(format!(\"Could not deserialize router_type: {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1110, "code_snippet": "family_filter(format!(\"^{ROUTER_FAMILY}$\")),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1126, "code_snippet": "let column = format!(\"chid:{}\", channel_id.as_hyphenated());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1139, "code_snippet": "cq_filter.set_column_qualifier_regex_filter(format!(\"^{column}$\").into_bytes());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1172, "code_snippet": "let row_key = format!(\"{}#{}\", uaid.simple(), message.chidmessageid());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1332, "code_snippet": "let row_key = format!(\"{}#{}\", uaid.simple(), chidmessageid);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1352, "code_snippet": "let start_key = format!(\"{}#01:\", uaid.simple());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1353, "code_snippet": "let end_key = format!(\"{}#02:\", uaid.simple());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1364, "code_snippet": "filters.push(family_filter(format!(\"^{MESSAGE_TOPIC_FAMILY}$\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1407, "code_snippet": "format!(\"{}#02:{}z\", uaid.simple(), ts)", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1409, "code_snippet": "format!(\"{}#02:\", uaid.simple())", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1411, "code_snippet": "let end_key = format!(\"{}#03:\", uaid.simple());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1434, "code_snippet": "filters.push(family_filter(format!(\"^{MESSAGE_FAMILY}$\")));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\row.rs", "line_number": 52, "code_snippet": ".ok_or_else(|| DbError::Integrity(format!(\"Expected column: {column}\"), None))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\main.rs", "line_number": 42, "code_snippet": "let host_port = format!(\"{}:{}\", &settings.host, &settings.port);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\server.rs", "line_number": 61, "code_snippet": "let bind_address = format!(\"{}:{}\", settings.host, settings.port);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\server.rs", "line_number": 67, "code_snippet": ".map_err(|e| ApiErrorKind::General(format!(\"Configuration Error: {e}\")))?,", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\server.rs", "line_number": 104, "code_snippet": "ApiErrorKind::General(format!(\"Could not initialize Reliability Report: {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 87, "code_snippet": "crypto_keys: format!(\"[{}]\", Fernet::generate_key()),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 192, "code_snippet": ".map_err(|e| ConfigError::Message(format!(\"Invalid tracking key: {e:?}\")))?,", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 202, "code_snippet": "format!(\"{}://{}:{}\", self.scheme, self.host, self.port)", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 317, "code_snippet": "let port = format!(\"{}__PORT\", super::ENV_PREFIX).to_uppercase();", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 318, "code_snippet": "let timeout = format!(\"{}__FCM__TIMEOUT\", super::ENV_PREFIX).to_uppercase();", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'println!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 128, "code_snippet": "println!(\"Bad configuration: {:?}\", &error_msg);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 138, "code_snippet": "assert!(get_token_from_auth_header(&format!(\"bearer {}\", &token)).is_some());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 139, "code_snippet": "assert!(get_token_from_auth_header(&format!(\"webpush {}\", &token)).is_some());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 140, "code_snippet": "assert!(get_token_from_auth_header(&format!(\"random {}\", &token)).is_none());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification.rs", "line_number": 108, "code_snippet": ".incr(&format!(\"updates.notification.encoding.{encoding}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "line_number": 174, "code_snippet": "ApiErrorKind::InvalidEncryption(format!(\"Missing {header_name} header\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "line_number": 177, "code_snippet": "ApiErrorKind::InvalidEncryption(format!(\"Invalid {header_name} header\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "line_number": 180, "code_snippet": "ApiErrorKind::InvalidEncryption(format!(\"Missing {key} value in {header_name} header\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "line_number": 201, "code_snippet": "ApiErrorKind::InvalidEncryption(format!(\"Invalid {header_name} header\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Crypto Issues", "title": "Potential Crypto Issues Issue", "description": "Pattern 'static.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 428, "code_snippet": "static ref PRIV_KEY: Vec<u8> = b64_decode_std(", "impact": "Cryptographic weaknesses leading to data exposure", "recommendation": "Use cryptographically secure random number generators", "cwe_id": "CWE-338", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 152, "code_snippet": ".incr_raw(&format!(\"updates.vapid.draft{:02}\", vapid.vapid.version()))?;", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 352, "code_snippet": "return Err(VapidError::InvalidVapid(format!(\"Missing required {e}\")).into());", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'from_slice\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 132, "code_snippet": "let uaid = Uuid::from_slice(&token[..16]).unwrap();", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'from_slice\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 133, "code_snippet": "let channel_id = Uuid::from_slice(&token[16..32]).unwrap();", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\common.rs", "line_number": 134, "code_snippet": "&format!(\"upstream_{status}\"),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\webpush.rs", "line_number": 224, "code_snippet": "let url = format!(\"{}/push/{}\", node_id, notification.subscription.user.uaid);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\webpush.rs", "line_number": 243, "code_snippet": "let url = format!(\"{node_id}/notif/{uaid}\");", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\webpush.rs", "line_number": 332, "code_snippet": ".join(&format!(\"/m/{}\", notification.message_id))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\health.rs", "line_number": 66, "code_snippet": "Ok(format!(\"OK: {}\", keys.join(\",\")))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\registration.rs", "line_number": 44, "code_snippet": ".map_err(|e| ApiErrorKind::General(format!(\"User::builder error: {e}\")))?;", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\reliability.rs", "line_number": 13, "code_snippet": ".map_err(|e| ApiErrorKind::General(format!(\"Reliability report error: {e}\")).into())", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\router.rs", "line_number": 180, "code_snippet": ".unwrap_or_else(|| format!(\"com.mozilla.org.{name}\")),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\router.rs", "line_number": 204, "code_snippet": ".map(|r| format!(\"{:?}\", r.reason))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\router.rs", "line_number": 510, "code_snippet": ".join(&format!(\"/m/{}\", notification.message_id))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'serde_json::from_str\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\settings.rs", "line_number": 48, "code_snippet": "serde_json::from_str(&self.channels)", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 100, "code_snippet": "\"ttl\": format!(\"{ttl}s\"),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 119, "code_snippet": ".header(\"Authorization\", format!(\"Bearer {token}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 174, "code_snippet": "message: format!(\"Unknown reason: {:?}\", status.to_string()),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 246, "code_snippet": "server.mock(\"POST\", format!(\"/v1/projects/{id}/messages:send\").as_str())", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 284, "code_snippet": ".match_header(\"Authorization\", format!(\"Bearer {ACCESS_TOKEN}\").as_str())", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'from_slice\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "line_number": 144, "code_snippet": "let data: FcmResponse = serde_json::from_slice(&raw_data).map_err(|e| {", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\router.rs", "line_number": 209, "code_snippet": ".join(&format!(\"/m/{}\", notification.message_id))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'serde_json::from_str\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\settings.rs", "line_number": 71, "code_snippet": "serde_json::from_str(&self.server_credentials)", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\router.rs", "line_number": 78, "code_snippet": "StubError::General(format!(\"Unknown App ID: {}\", app_id.to_owned())).into(),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\router.rs", "line_number": 114, "code_snippet": "format!(\"{}/m/123\", self.settings.url),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\unidentified.rs", "line_number": 187, "code_snippet": ".map_err(|e| SMErrorKind::Internal(format!(\"User::builder error: {e}\")))?;", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\unidentified.rs", "line_number": 260, "code_snippet": "assert!(format!(\"{err}\").contains(MessageType::Hello.as_ref()));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\unidentified.rs", "line_number": 274, "code_snippet": "assert!(format!(\"{err}\").contains(MessageType::Hello.as_ref()));", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'serde_json::from_str\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\unidentified.rs", "line_number": 285, "code_snippet": "let msg: ClientMessage = serde_json::from_str(&js).unwrap();", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\identified\\mod.rs", "line_number": 266, "code_snippet": ".put(format!(\"{}/notif/{}\", node_id, uaid.as_simple()))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\identified\\on_client_msg.rs", "line_number": 60, "code_snippet": "SMError::invalid_message(format!(\"Invalid channelID: {channel_id_str}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'from_slice\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\test.rs", "line_number": 36, "code_snippet": "serde_json::from_slice(&bytes).unwrap()", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\app_state.rs", "line_number": 102, "code_snippet": "ConfigError::Message(format!(\"Could not start Reliability connection: {e:?}\"))", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 136, "code_snippet": "crypto_key: format!(\"[{}]\", Fernet::generate_key()),", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 187, "code_snippet": "format!(\"{}:{}\", url, self.router_port)", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 194, "code_snippet": "let url = format!(\"{}://{}\", self.endpoint_scheme, self.endpoint_hostname,);", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 196, "code_snippet": "format!(\"{}:{}\", url, self.endpoint_port)", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 310, "code_snippet": "let port = format!(\"{ENV_PREFIX}__PORT\").to_uppercase();", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 311, "code_snippet": "let msg_limit = format!(\"{ENV_PREFIX}__MSG_LIMIT\").to_uppercase();", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 312, "code_snippet": "let fernet = format!(\"{ENV_PREFIX}__CRYPTO_KEY\").to_uppercase();", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Injection", "title": "Potential Injection Issue", "description": "Pattern 'format!\\(.*\\{.*\\}.*\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\protocol.rs", "line_number": 47, "code_snippet": "format!(r#\"Expected messageType=\"{}\"\"#, self.as_ref())", "impact": "Code execution or data manipulation through injection attacks", "recommendation": "Use parameterized queries and input sanitization", "cwe_id": "CWE-94", "cvss_score": null}, {"severity": "HIGH", "category": "Deserialization", "title": "Potential Deserialization Issue", "description": "Pattern 'serde_json::from_str\\(' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\protocol.rs", "line_number": 128, "code_snippet": ".or_else(|_| serde_json::from_str(s))", "impact": "Remote code execution through malicious payloads", "recommendation": "Validate and sanitize all deserialized data", "cwe_id": "CWE-502", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'lazy_static!' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "line_number": 68, "code_snippet": "lazy_static! {", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Input Validation", "title": "Potential Input Validation Issue", "description": "Pattern '\\.parse\\(\\)\\.unwrap\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 107, "code_snippet": "let uaid: Uuid = \"729e5104f5f04abc9196085340317dea\".parse().unwrap();", "impact": "Data corruption or unexpected application behavior", "recommendation": "Implement comprehensive input validation", "cwe_id": "CWE-20", "cvss_score": null}, {"severity": "MEDIUM", "category": "Input Validation", "title": "Potential Input Validation Issue", "description": "Pattern '\\.parse\\(\\)\\.unwrap\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 119, "code_snippet": "let uaid: Uuid = \"729e5104f5f04abc9196085340317dea\".parse().unwrap();", "impact": "Data corruption or unexpected application behavior", "recommendation": "Implement comprehensive input validation", "cwe_id": "CWE-20", "cvss_score": null}, {"severity": "MEDIUM", "category": "Input Validation", "title": "Potential Input Validation Issue", "description": "Pattern '\\.parse\\(\\)\\.unwrap\\(\\)' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 134, "code_snippet": "let uaid: Uuid = \"729e5104f5f04abc9196085340317dea\".parse().unwrap();", "impact": "Data corruption or unexpected application behavior", "recommendation": "Implement comprehensive input validation", "cwe_id": "CWE-20", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'lazy_static!' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "line_number": 13, "code_snippet": "lazy_static! {", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'lazy_static!' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\router_data_input.rs", "line_number": 9, "code_snippet": "lazy_static! {", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'lazy_static!' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "line_number": 427, "code_snippet": "lazy_static! {", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'RwLock<' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\app_state.rs", "line_number": 32, "code_snippet": "pub broadcaster: <PERSON><RwLock<BroadcastChangeTracker>>,", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'lazy_static!' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "line_number": 22, "code_snippet": "lazy_static! {", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'RwLock<' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\megaphone.rs", "line_number": 24, "code_snippet": "broadcaster: <PERSON><PERSON><RwLock<BroadcastChangeTracker>>,", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'RwLock<' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\megaphone.rs", "line_number": 79, "code_snippet": "broadcaster: <PERSON><PERSON><RwLock<BroadcastChangeTracker>>,", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "MEDIUM", "category": "Race Conditions", "title": "Potential Race Conditions Issue", "description": "Pattern 'RwLock<' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\registry.rs", "line_number": 26, "code_snippet": "clients: RwLock<HashMap<Uuid, RegisteredClient>>,", "impact": "Data corruption or privilege escalation", "recommendation": "Use proper synchronization mechanisms", "cwe_id": "CWE-362", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1173, "code_snippet": "debug!(\"🗄️ Saving message {} :: {:?}\", &row_key, &message);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "line_number": 1333, "code_snippet": "debug!(\"🉑🔥 Deleting message {}\", &row_key);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 166, "code_snippet": "debug!(\"🔐 Fernet keys: {:?}\", &key);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "line_number": 230, "code_snippet": "debug!(\"🔍 Checking {:?} {}\", &vapid.public_key, {", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*token' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "line_number": 42, "code_snippet": "debug!(\"expected: {:?}, recv'd {:?}\", &expected_token, &token);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*token' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\registration.rs", "line_number": 109, "code_snippet": "debug!(\"🌍 Updating the token of UAID {uaid} with the {router_type} router\");", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\webpush.rs", "line_number": 37, "code_snippet": "debug!(\"Deleting notification with sort-key {}\", sort_key);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}, {"severity": "LOW", "category": "Info Disclosure", "title": "Potential Info Disclosure Issue", "description": "Pattern 'debug!\\(.*key' detected in security-sensitive context", "file_path": "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\app_state.rs", "line_number": 51, "code_snippet": "debug!(\"🔐 Fernet keys: {:?}\", &crypto_key);", "impact": "Sensitive information exposure to unauthorized parties", "recommendation": "Remove sensitive information from logs and error messages", "cwe_id": "CWE-200", "cvss_score": null}], "analyzed_files": ["mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\error.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification_headers.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\error.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\lib.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\error.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\util\\user_agent.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\common.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\notification.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\middleware\\mod.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\settings.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\logging.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\webpush.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\router_data_input.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\error.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\webpush.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\lib.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\registration.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\headers\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\app_state.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\lib.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\settings.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\router.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\lib.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\pool.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\client.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\lib.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-settings\\src\\lib.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\protocol.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\client.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\util\\timing.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\errors.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\router.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\error.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\token_info.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\reliability.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\new_channel_data.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\mock.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\test.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\util\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\broadcast.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\metric_name.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\settings.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\metadata.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\user.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\ping.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\error.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\apns\\mod.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\metrics.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\error.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\endpoint.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\mod.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\headers\\crypto_key.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\subscription.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\mod.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\routing.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\error.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\identified\\on_client_msg.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\registry.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\sentry.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\routers.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\main.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\identified\\on_server_notif.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\settings.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\headers\\util.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\redis_util.rs", "mozilla\\autopush-rs-master\\autopush-common\\build.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\test.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\message_id.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\unidentified.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\client.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\tags.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\middleware\\sentry.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\cell.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\stub\\router.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\session.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\models.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\error.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\routes.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\reporter.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\notification.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\merge.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\headers\\vapid.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\auth.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\mod.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\health.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\src\\handler.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routers\\fcm\\mod.rs", "mozilla\\autopush-rs-master\\autoconnect\\src\\main.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\authorization_check.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\metrics.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-ws\\autoconnect-ws-sm\\src\\identified\\mod.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\test_support.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\server.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\megaphone.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-web\\src\\dockerflow.rs", "mozilla\\autopush-rs-master\\autopush-common\\src\\db\\bigtable\\bigtable_client\\row.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\routes\\reliability.rs", "mozilla\\autopush-rs-master\\autoconnect\\autoconnect-common\\src\\test_support.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\registration_path_args.rs", "mozilla\\autopush-rs-master\\autoendpoint\\src\\extractors\\registration_path_args_with_uaid.rs"]}