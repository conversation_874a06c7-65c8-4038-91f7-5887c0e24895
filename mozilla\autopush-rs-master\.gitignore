.DS_Store
syntax:glob
.svn
.coverage
*.pyc
*.egg-info
*.egg
*~
build
dist
docs/_build
*.xml
html_coverage
.hgignore
.idea
*.iml
site-packages/*
lib-python/*
bin/*
include/*
lib_pypy/*
*.swp
pypy/
./src/
.tox/
.eggs/
autopush_rs/target
autopush_rs/_native*
target
*.rs.bk
requirements.txt
test-requirements.txt
venv
.python-version

# docs
docs/output
docs/old

# Local configs
*.local.toml
.cargo/config
.vscode/*

# circleCI
workspace

# For poetry install
.install.stamp