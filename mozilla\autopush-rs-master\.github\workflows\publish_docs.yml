name: Publish docs to pages
run-name: ${{ github.actor }} is publishing docs
on:
    workflow_dispatch:  # Allow manual triggering
    push:
        branches: [master,main]
        # paths: ['docs/**.md']  # API docs need building always

concurrency:
    group: github-pages
    cancel-in-progress: false  # Skip any intermediate builds but finish deploying

jobs:
    build:
        runs-on: ubuntu-latest
        env:
            MDBOOK_ENV: 0.4.51
            MERMAID_ENV: 0.15.0
            DEST_DIR: /home/<USER>/.cargo/bin
        steps:
            - uses: actions/checkout@v4
            - uses: actions/cache@v4
              with:
                path: |
                  ~/.cargo/bin/
                  ~/.cargo/registry/
                  ~/.cargo/git/
                  target/
                key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
            - name: Install mdBook
              run: |
                export PATH=$PATH:$DEST_DIR
                curl -sSL "https://github.com/rust-lang/mdBook/releases/download/v$MDBOOK_ENV/mdbook-v$MDBOOK_ENV-x86_64-unknown-linux-gnu.tar.gz" | tar -xz --directory $DEST_DIR
                curl -sSL "https://github.com/badboy/mdBook-mermaid/releases/download/v$MERMAID_ENV/mdbook-mermaid-v$MERMAID_ENV-x86_64-unknown-linux-gnu.tar.gz" | tar -xz --directory $DEST_DIR
            - name: Build docs
              run: cd docs && sh make_book.sh
            - name: Upload artifact
              uses: actions/upload-pages-artifact@v3
              with:
                path: ./docs/output

    deploy:
        needs: build
        permissions:
            pages: write
            id-token: write
        environment:
            name: github-pages
            url: ${{ steps.deployment.outputs.page_url }}
        runs-on: ubuntu-latest
        steps:
            - name: Deploy to GitHub Pages
              id: depolyment
              uses: actions/deploy-pages@v4
