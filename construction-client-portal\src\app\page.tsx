'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/loading';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isLoading, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="text-center">
        <div className="mb-6">
          <div className="text-white font-bold text-2xl">
            <span className="text-3xl">B</span>
            <span className="text-sm">UILDERS</span>
            <br />
            <span className="text-sm">BY DESIGN</span>
            <span className="text-sm ml-1">GROUP</span>
          </div>
        </div>
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-400">Loading Client Portal...</p>
      </div>
    </div>
  );
}
