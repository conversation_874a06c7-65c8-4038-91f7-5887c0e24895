# These environment variables must be set in CircleCI UI
#
# DOCKERHUB_CONNECT_REPO - autoconnect docker hub repo, format: <username>/<repo>
# DOCKERHUB_ENDPOINT_REPO - autoendpoint docker hub repo, format: <username>/<repo>
# DOCKER_EMAIL   - login info for docker hub
# DOCKER_USER
# DOCKER_PASS
# DOCKERHUB_LOAD_TEST_REPO - Docker Hub repo for load tests, format: <username>/<repo>
# DOCKER_LOAD_TEST_PASS - Docker Hub load test repo password
# DOCKER_LOAD_TEST_USER - Docker Hub load test repo user
#
# These environment variables are necessary to authenticate with G<PERSON> and upload images to GAR
# GCP_GAR_PROJECT_ID - GCP project ID for GAR repo
# GCP_GAR_REPO - Name of GAR repo
# GCP_OIDC_PROJECT_NUMBER - GCP project number for Workload Identity Pool/Provider
# GCP_OIDC_SERVICE_ACCOUNT_EMAIL - GCP service account email
# GCP_OIDC_WIP_ID - GCP Workload Identity Pool ID
# GCP_OIDC_WIP_PROVIDER_ID - GCP Workload Identity Pool Provider ID

version: 2.1

orbs:
  gcp-cli: circleci/gcp-cli@3.3.0
  gcp-gcr: circleci/gcp-gcr@0.16.3

executors:
  audit-executor:
    docker:
      # NOTE: update version for all # RUST_VER
      - image: rust:1.86
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
  python-checks-executor:
    docker:
      - image: python:3.12-slim-bookworm
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
  rust-checks-executor:
    docker:
      - image: python:3.12-slim-bookworm
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
        environment:
          RUST_BACKTRACE: 1
          RUST_TEST_THREADS: 1
  unit-test-executor:
    docker:
      - image: python:3.12-slim-bookworm
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
        environment:
          RUST_BACKTRACE: 1
          RUST_TEST_THREADS: 1
      - image: google/cloud-sdk:latest
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
        command: gcloud beta emulators bigtable start --host-port=localhost:8086
  integration-test-executor:
    docker:
      - image: cimg/base:2025.02
        environment:
          RUST_BACKTRACE: 1
  build-executor:
    docker:
      - image: docker:18.03.0-ce
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
  build-reliability-cron:
    docker:
      - image: docker:18.03.0-ce
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
  build-test-container-executor:
    docker:
      - image: cimg/base:2025.02



commands:
  docker_login:
    parameters:
      load:
        type: boolean
        default: False
    steps:
      - run:
          name: Login to Dockerhub
          command: |
            USER="${DOCKER_USER}"
            PASS="${DOCKER_PASS}"
            if <<parameters.load>>; then
              echo "Using load test repository credentials"
              USER="${DOCKER_LOAD_TEST_USER}"
              PASS="${DOCKER_LOAD_TEST_PASS}"
            fi
            if [ "${USER}" == "" ] || [ "${PASS}" == "" ]; then
              echo "Skipping Login to Dockerhub, no credentials."
            else
              echo "${PASS}" | docker login -u="${USER}" --password-stdin
            fi
  setup_rust:
    steps:
      - run:
          name: Set up Rust
          command: |
            apt update
            apt install build-essential curl libstdc++6 libstdc++-12-dev clang libssl-dev pkg-config -y
            apt install cmake -y
            # RUST_VER
            curl https://sh.rustup.rs -sSf | sh -s -- --default-toolchain 1.86 -y
            export PATH=$PATH:$HOME/.cargo/bin
            echo 'export PATH=$PATH:$HOME/.cargo/bin' >> $BASH_ENV
            rustc --version
  build_applications:
    steps:
      - run:
          name: Build Applications
          command: cargo build --features=emulator
  setup_bigtable:
    steps:
      - run:
          name: Setup Bigtable
          command: scripts/setup_bt.sh
  setup_cbt:
    steps:
      - run:
          name: Set up cbt
          command: |
            echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
            curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg
            apt-get update -y
            apt install google-cloud-cli-cbt -y
  create_test_result_workspace:
    steps:
      - run:
          name: Create Workspace
          command: mkdir -p workspace/test-results
  restore_test_cache:
    parameters:
      cache_key:
        type: string
    steps:
      - restore_cache:
          name: Restoring Rust cache
          key: <<parameters.cache_key>>
  save_test_cache:
    parameters:
      cache_key:
        type: string
    steps:
      - save_cache:
          name: Save Rust cache
          key: <<parameters.cache_key>>
          paths:
            - target
            - ~/.cargo/registry
            - ~/.cargo/git
  setup_python:
    steps:
      - run:
          name: Set up Python
          command: |
            pip install --upgrade pip
            pip install poetry==2.0.0

  upload_to_gcs:
    parameters:
      source:
        type: string
      destination:
        type: string
      extension:
        type: enum
        enum: ["xml", "json"]
    steps:
      - run:
          name: Upload << parameters.source >> << parameters.extension >> Files to GCS
          when: always  # Ensure the step runs even if previous steps, like test runs, fail
          command: |
            if [ "$CIRCLE_BRANCH" = "master" ]; then
              FILES=$(ls -1 << parameters.source>>/*.<< parameters.extension>> )
              if [ -z "$FILES" ]; then
                echo "No << parameters.extension >> files found in << parameters.source >>/"
                exit 1
              fi
              gsutil cp $FILES << parameters.destination >>
            else
              echo "Skipping artifact upload, not on 'master' branch."
            fi

jobs:
  audit:
    executor: audit-executor
    resource_class: large
    steps:
      - checkout
      - run:
          name: Setup cargo-audit
          command: |
            rustc --version
            cargo install cargo-audit
      - run:
          command: cargo audit

  python-checks:
    executor: python-checks-executor
    steps:
      - checkout
      - run:
          name: Set up system
          command: |
            apt update
            apt install git -y
            apt install cmake clang -y
      - run:
          name: Set up Python
          command: |
            pip install --upgrade pip
            pip install poetry==2.0.0
      - run:
          name: isort, black, flake8, pydocstyle and mypy
          command: make lint

  test-integration:
    executor: integration-test-executor
    resource_class: large
    steps:
      - checkout
      - gcp-cli/setup
      - setup_remote_docker:
          docker_layer_caching: true
      - attach_workspace:
          at: /tmp/cache
      - create_test_result_workspace
      - run:
          name: Restore Docker image cache
          command: |
            docker load -i /tmp/cache/autopush-integration-tests.tar
            docker tag autopush-integration-tests integration-tests
      - run:
          name: Integration tests
          command: make integration-test
      - store_test_results:
          path: workspace/test-results
      - upload_to_gcs:
          source: workspace/test-results
          destination: gs://ecosystem-test-eng-metrics/autopush-rs/junit
          extension: xml

  test-unit:
    executor: unit-test-executor
    resource_class: 2xlarge
    environment:
      BIGTABLE_EMULATOR_HOST: localhost:8086
    steps:
      - checkout
      # Need to download the poetry.lock files, so we can use their
      # checksums in restore_cache.
      - restore_test_cache:
          cache_key: rust-v2-cache-{{ checksum "Cargo.lock" }}
      - create_test_result_workspace
      - setup_rust
      - setup_cbt
      - setup_bigtable
      - gcp-cli/setup
      - run:
          name: Install cargo-nextest
          command: curl -LsSf https://get.nexte.st/latest/linux | tar zxf - -C ${CARGO_HOME:-~/.cargo}/bin
      - run:
          name: Echo Rust version
          command: |
            rustc --version
      - run:
          name: Install cargo-llvm-cov
          command: cargo install cargo-llvm-cov
      # Note: This build can potentially exceed the amount of memory available to the CircleCI instance.
      # We've seen that limiting the number of jobs helps reduce the frequency of this. (Note that
      # when doing discovery, we found that the docker image `meminfo` and `cpuinfo` often report
      # the machine level memory and CPU which are far higher than the memory allocated to the docker
      # instance. This may be causing rust to be overly greedy triggering the VM to OOM the process.)
      - run:
          name: Unit tests
          command: |
            make unit-test
      - store_test_results:
          path: workspace/test-results
      - upload_to_gcs:
          source: workspace/test-results
          destination: gs://ecosystem-test-eng-metrics/autopush-rs/junit
          extension: xml
      - upload_to_gcs:
          source: workspace/test-results
          destination: gs://ecosystem-test-eng-metrics/autopush-rs/coverage
          extension: json
      - save_test_cache:
          cache_key: rust-v2-cache-{{ checksum "Cargo.lock" }}

  rust-checks:
    executor: rust-checks-executor
    resource_class: large
    environment:
      BIGTABLE_EMULATOR_HOST: localhost:8086
    steps:
      - checkout
      # Need to download the poetry.lock files, so we can use their
      # checksums in restore_cache.
      - restore_test_cache:
          cache_key: rust-v2-cache-{{ checksum "Cargo.lock" }}
      - setup_rust
      - run:
          name: Echo Rust Version
          command: |
            rustc --version
      - run:
          name: Check formatting
          command: |
            cargo fmt -- --check
            cargo clippy --all --all-targets --all-features -- -D warnings --deny=clippy::dbg_macro

  build:
    executor: build-executor
    resource_class: large
    working_directory: /dockerflow
    parameters:
      image:
        type: string
      crate:
        type: string
      binary:
        type: string
    steps:
      # Install these packages before checkout because git may not exist or work
      - run:
          name: Install Docker build dependencies
          command: apk add --no-cache openssh-client git clang
      - checkout
      - setup_remote_docker
      - docker_login
      - run:
          name: Create a version.json
          command: |
            # create a version.json per https://github.com/mozilla-services/Dockerflow/blob/master/docs/version_object.md
            printf '{"commit":"%s","version":"%s","source":"https://github.com/%s/%s","build":"%s"}\n' \
            "$CIRCLE_SHA1" \
            "$CIRCLE_TAG" \
            "$CIRCLE_PROJECT_USERNAME" \
            "$CIRCLE_PROJECT_REPONAME" \
            "$CIRCLE_BUILD_URL" > version.json
      - run:
          name: Build Docker image
          command: |
            docker build -t <<parameters.image>> \
              --build-arg CRATE=<<parameters.crate>> \
              --build-arg BINARY=<<parameters.binary>> .
      # save the built docker container into CircleCI's workspace cache. This is
      # required since Workflows do not have the same remote docker instance.
      - run:
          name: docker save <<parameters.image>>
          command: mkdir -p /tmp/cache; docker save -o /tmp/cache/<<parameters.image>>.tar "<<parameters.image>>"
      - persist_to_workspace:
          root: /tmp/cache
          paths:
            - <<parameters.image>>.tar

  # Create the reliability cron docker image. This is a singleton that does some
  # clean-up and reporting for the Push Reliability task.
  build-reliability-cron:
    executor: build-reliability-cron
    resource_class: small
    working_directory: /app
    parameters:
      image:
        type: string
      tag:
        type: string
    steps:
      # Install these packages before checkout because git may not exist or work
      - run:
          name: Install Docker build dependencies
          command: apk add --no-cache openssh-client git clang
      - checkout
      - setup_remote_docker
      - docker_login
      - run:
          name: Build cron docker image
          command: |
            docker build --tag <<parameters.image>>:<<parameters.tag>> -f ./scripts/reliability/Dockerfile ./scripts/reliability
      - run:
          name: Save cron docker image
          # note: deploy always expects the image to be saved in /tmp/cache
          command: |
            mkdir -p /tmp/cache
            docker save -o /tmp/cache/<<parameters.image>>.tar "<<parameters.image>>"
      - persist_to_workspace:
          root: /tmp/cache
          paths:
            - <<parameters.image>>.tar

  build-test-container:
    executor: build-test-container-executor
    parameters:
      image:
        type: string
      path:
        type: string
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: Build Image
          command: docker build -t << parameters.image >> -f ./tests/<< parameters.path >>/Dockerfile .
      - run:
          name: Save Docker Image to Workspace
          command: |
            mkdir -p /tmp/cache
            docker save -o /tmp/cache/<< parameters.image >>.tar << parameters.image >>
      - persist_to_workspace:
          root: /tmp/cache
          paths:
            - << parameters.image >>.tar

  build-integration-test-container:
    executor: build-test-container-executor
    resource_class: large
    parameters:
      image:
        type: string
      path:
        type: string
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: Build Image
          command: |
            docker buildx build --target planner -f ./tests/<< parameters.path >>/Dockerfile .
            docker buildx build --target cacher -f ./tests/<< parameters.path >>/Dockerfile .
            docker buildx build --target builder -f ./tests/<< parameters.path >>/Dockerfile .
            docker buildx build --target integration-tests -t << parameters.image >> -f ./tests/<< parameters.path >>/Dockerfile .
      - run:
          name: Save Docker Image to Workspace
          command: |
            mkdir -p /tmp/cache
            docker save -o /tmp/cache/<< parameters.image >>.tar << parameters.image >>
      - persist_to_workspace:
          root: /tmp/cache
          paths:
            - << parameters.image >>.tar

  deploy:
    executor: gcp-gcr/default
    parameters:
      build_tag:
        type: string
        default: build
      image:
        type: string
      registry-url:
        type: string
        default: us-docker.pkg.dev
    steps:
      # gcr-auth parameters:
      # https://circleci.com/developer/orbs/orb/circleci/gcp-gcr#commands-gcr-auth
      - gcp-gcr/gcr-auth:
          gcp_cred_config_file_path: ~/gcp_cred_config.json
          google-project-id: GCP_GAR_PROJECT_ID
          google_project_number: GCP_OIDC_PROJECT_NUMBER
          registry-url: <<parameters.registry-url>>
          service_account_email: GCP_OIDC_SERVICE_ACCOUNT_EMAIL
          use_oidc: true
          workload_identity_pool_id: GCP_OIDC_WIP_ID
          workload_identity_pool_provider_id: GCP_OIDC_WIP_PROVIDER_ID
      - attach_workspace:
          at: /tmp/cache
      - run:
          name: Restore Docker image cache
          command: docker load -i /tmp/cache/<<parameters.image>>.tar
      # This is the easiest way to tag multiple images using different
      # conditions for the GAR_TAG variable in the smallest amount of code.
      #
      # You can find other jobs and commands you can use with this orb that
      # include tagging here:
      # https://circleci.com/developer/orbs/orb/circleci/gcp-gcr
      - run:
          name: Tag image
          command: |
            if [ ! -z "${CIRCLE_TAG}" ]; then
              echo "export GAR_TAG=${CIRCLE_TAG}" >> $BASH_ENV
            else
              echo "export GAR_TAG=${CIRCLE_BRANCH}" >> $BASH_ENV
            fi
              echo "export GAR_IMAGE=\"<<parameters.registry-url>>/${GCP_GAR_PROJECT_ID}/${GCP_GAR_REPO}/<<parameters.image>>\"" >> $BASH_ENV
              source $BASH_ENV
              docker tag <<parameters.image>> $GAR_IMAGE:$GAR_TAG
              docker tag <<parameters.image>> $GAR_IMAGE:latest
      # push-image parameters:
      # https://circleci.com/developer/orbs/orb/circleci/gcp-gcr#commands-push-image
      - gcp-gcr/push-image:
          image: "${GCP_GAR_REPO}/<<parameters.image>>"
          google-project-id: GCP_GAR_PROJECT_ID
          registry-url: <<parameters.registry-url>>
          tag: $GAR_TAG,latest

workflows:
  build-test-deploy:
    jobs:
      - audit:
          filters:
            tags:
              only: /.*/
      - python-checks:
          filters:
            tags:
              only: /.*/
      - test-integration:
          name: Integration Tests
          requires:
            - Build Integration Test Image
            - python-checks
          filters:
            tags:
              only: /.*/
      - test-unit:
          name: Unit Tests
          filters:
            tags:
              only: /.*/
      - rust-checks:
          name: Rust Formatting Check
          filters:
            tags:
              only: /.*/
      - build:
          name: build-autoconnect
          image: autoconnect
          crate: autoconnect
          binary: autoconnect
          filters:
            tags:
              only: /.*/
      - build:
          name: build-autoendpoint
          image: autoendpoint
          crate: autoendpoint
          binary: autoendpoint
          filters:
            tags:
              only: /.*/

      - build-reliability-cron:
          name: build-reliability-cron
          image: autopush-reliability-cron
          tag: latest
          filters:
            tags:
              only: /.*/
            branches:
              only: master

      - build-test-container:
          name: Build Load Test Image
          image: autopush-load-tests
          path: load
          filters:
            tags:
              only: /.*/
      - build-test-container:
          name: Build End-To-End Test Image
          image: autopush-end-to-end-tests
          path: notification
          filters:
            tags:
              only: /.*/
            branches:
              only: master
      - build-integration-test-container:
          name: Build Integration Test Image
          image: autopush-integration-tests
          path: integration
          filters:
            tags:
              only: /.*/

      # Comment out the following three sections for local CircleCI testing.
      - deploy:
          name: deploy-autoconnect
          image: autoconnect
          requires:
            - build-autoconnect
            - Unit Tests
            - Integration Tests
            - Rust Formatting Check
          filters:
            tags:
              only: /.*/
            branches:
              only: master

      - deploy:
          name: deploy-autoendpoint
          image: autoendpoint
          requires:
            - build-autoendpoint
            - Unit Tests
            - Integration Tests
            - Rust Formatting Check
          filters:
            tags:
              only: /.*/
            branches:
              only: master

      - deploy:
          name: deploy-reliability-cron
          image: autopush-reliability-cron
          requires:
            - build-reliability-cron
          filters:
            tags:
              only: /.*/
            branches:
              only: master

      - deploy:
          name: Push Load Test Image
          image: autopush-load-tests
          requires:
            - Build Load Test Image
            - python-checks
          filters:
            tags:
              only: /.*/
            branches:
              only: master
      - deploy:
          name: Push End-To-End Test Image
          image: autopush-end-to-end-tests
          requires:
            - Build End-To-End Test Image
            - python-checks
          filters:
            tags:
              only: /.*/
            branches:
              only: master
      - deploy:
          name: Push Integration Test Image
          image: autopush-integration-tests
          requires:
            - Build Integration Test Image
            - Integration Tests
            - python-checks
          filters:
            tags:
              only: /.*/
            branches:
              only: master
