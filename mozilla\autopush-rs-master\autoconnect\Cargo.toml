[package]
name = "autoconnect"
version.workspace = true
authors.workspace = true
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-http.workspace = true
actix-server = "2.3"
actix-service = "2.0"
actix-web.workspace = true
docopt = "1.1"
env_logger.workspace = true
sentry.workspace = true
serde.workspace = true
slog-scope.workspace = true

autoconnect_ws.workspace = true
autoconnect_common.workspace = true
autoconnect_settings.workspace = true
autoconnect_web.workspace = true
autopush_common.workspace = true

[features]
default = ["bigtable", "reliable_report"]
bigtable = ["autopush_common/bigtable", "autoconnect_settings/bigtable"]
emulator = ["bigtable"]
log_vapid = []
reliable_report = [
    "autoconnect_common/reliable_report",
    "autoconnect_settings/reliable_report",
    "autoconnect_web/reliable_report",
    "autoconnect_ws/reliable_report",
    "autopush_common/reliable_report",
]
